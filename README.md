# 知识图谱构建系统

> **核心理念：不重复造轮子，充分利用开源生态**

一个基于开源技术栈的企业级知识图谱构建系统，支持从非结构化文本到结构化知识图谱的完整流程，并提供基于图的检索增强生成(Graph RAG)服务。

## 🌟 核心特性

- **🔧 基于开源生态**：充分利用LangChain、Neo4j、vLLM等成熟开源项目
- **🚀 企业级部署**：支持vLLM、Ollama等开源LLM部署方案
- **📚 多模态处理**：集成MinerU等专业文档解析工具
- **🔍 智能检索**：基于Microsoft GraphRAG方法论的双模式检索
- **🎯 标准兼容**：遵循OpenAI API标准，确保兼容性
- **🔄 模块化设计**：基于适配器模式，支持组件灵活替换

## 🏗️ 技术架构

### 开源项目依赖

| 功能领域 | 开源项目 | 选择理由 |
|---------|---------|---------|
| **AI框架** | LangChain, LangGraph | 业界标准，生态完整 |
| **LLM部署** | vLLM, Ollama | 高性能，OpenAI兼容 |
| **图数据库** | Neo4j, NetworkX | 企业级，算法丰富 |
| **文档处理** | MinerU, PyMuPDF | 高保真，标准库 |
| **向量搜索** | Faiss, ChromaDB | 高性能，AI原生 |
| **社区检测** | leidenalg, python-louvain | 算法权威实现 |

### 架构图

```mermaid
graph TB
    subgraph "应用层"
        A[FastAPI Web服务] --> B[Streamlit界面]
    end

    subgraph "框架集成层"
        C[LangGraph智能体] --> D[LangChain工具]
    end

    subgraph "核心服务层"
        E[知识图谱构建器] --> F[文档处理器]
        G[GraphRAG服务] --> H[社区检测器]
    end

    subgraph "开源组件层"
        I[OpenAI SDK] --> J[Transformers]
        K[Neo4j Driver] --> L[NetworkX]
        M[MinerU] --> N[PyMuPDF]
        O[Faiss] --> P[ChromaDB]
    end

    A --> C
    C --> E
    E --> I
    E --> K
    F --> M
    G --> O
    H --> L
```

## 🚀 快速开始

### 环境要求

- Python 3.10+
- Neo4j 5.0+
- Docker（可选）

### 安装

```bash
# 克隆项目
git clone https://github.com/example/knowledge-graph-builder.git
cd knowledge-graph-builder

# 使用Poetry安装依赖（推荐）
poetry install

# 或使用pip安装
pip install -e .
```

### 配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

### 启动服务

```bash
# 启动Neo4j（使用Docker）
docker run --name neo4j \
    -p 7474:7474 -p 7687:7687 \
    -e NEO4J_AUTH=neo4j/password \
    neo4j:5.9

# 启动LLM服务（使用vLLM）
python -m vllm.entrypoints.openai.api_server \
    --model Qwen/Qwen2.5-7B-Instruct \
    --port 8000

# 启动知识图谱服务
poetry run kg-serve
```

## 📖 使用示例

### 基础使用

```python
from knowledge_graph_builder import KnowledgeGraphBuilder

# 初始化构建器
builder = KnowledgeGraphBuilder()

# 处理文档并构建知识图谱
documents = ["张三是ABC公司的工程师。", "李四在XYZ大学学习。"]
graph = builder.build_from_texts(documents)

# 查询知识图谱
result = builder.query("张三在哪家公司工作？")
print(result)
```

### LangGraph集成

```python
from knowledge_graph_builder.langgraph import KnowledgeGraphAgent

# 创建LangGraph智能体
agent = KnowledgeGraphAgent()

# 使用智能体
result = agent.invoke({
    "messages": [{"role": "user", "content": "分析公司组织结构"}],
    "documents": documents
})
```

## 🔧 开源项目集成

### LLM客户端

支持多种开源LLM部署方案：

```python
# vLLM部署
from knowledge_graph_builder.llm import VLLMClient
client = VLLMClient(model="qwen2.5-7b-instruct")

# Ollama部署
from knowledge_graph_builder.llm import OllamaClient
client = OllamaClient(model="qwen2.5:7b")

# 第三方API
from knowledge_graph_builder.llm import OpenAIClient
client = OpenAIClient()
```

### 文档处理

集成专业文档处理工具：

```python
# 使用MinerU进行高保真解析
from knowledge_graph_builder.document import MinerUProcessor
processor = MinerUProcessor()
content = processor.parse_pdf("document.pdf")

# 使用PyMuPDF进行标准PDF处理
from knowledge_graph_builder.document import PyMuPDFProcessor
processor = PyMuPDFProcessor()
content = processor.parse_pdf("document.pdf")
```

## 📚 文档

- [设计文档](./设计文档/design.md) - 详细的系统设计和架构说明
- [开源集成指南](./设计文档/opensource-integration-guide.md) - 开源项目集成详细指南
- [需求文档](./设计文档/requirements.md) - 系统需求和验收标准
- [开源项目总结](./设计文档/opensource-summary.md) - 开源项目使用总结

## 🤝 开源贡献

我们积极参与开源社区，并鼓励向上游项目贡献：

- 向LangChain、Neo4j等项目提交bug修复和功能改进
- 分享最佳实践和使用经验
- 参与开源项目的讨论和规划

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目的贡献：

- [LangChain](https://github.com/langchain-ai/langchain) - AI应用开发框架
- [Neo4j](https://neo4j.com/) - 图数据库
- [vLLM](https://github.com/vllm-project/vllm) - 高性能LLM推理引擎
- [MinerU](https://github.com/opendatalab/MinerU) - 文档解析工具
- [NetworkX](https://github.com/networkx/networkx) - 图分析库
- [Faiss](https://github.com/facebookresearch/faiss) - 向量搜索引擎

以及所有其他优秀的开源项目和贡献者！

---

**不重复造轮子，站在巨人的肩膀上构建企业级知识图谱系统。**