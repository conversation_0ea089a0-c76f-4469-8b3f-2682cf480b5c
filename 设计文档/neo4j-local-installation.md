# Neo4j本地安装指南

## 🎯 推荐版本

**Neo4j Community Edition 5.13.0**
- 与项目配置完全兼容
- 长期支持版本，稳定可靠
- 免费使用，功能完整

## 📦 安装方式

### 方式1：Neo4j Desktop（推荐新手）

#### 下载和安装
1. 访问 [Neo4j官网](https://neo4j.com/download/)
2. 点击 "Download Neo4j Desktop"
3. 填写邮箱（免费注册）
4. 下载对应操作系统的安装包
5. 运行安装程序

#### 创建数据库
1. 启动Neo4j Desktop
2. 点击 "New" → "Create project"
3. 在项目中点击 "Add" → "Local DBMS"
4. 配置数据库：
   - **Name**: knowledge-graph-mvp
   - **Password**: password123
   - **Version**: 5.13.0
5. 点击 "Create"
6. 点击数据库右侧的 "Start" 按钮

### 方式2：命令行安装

#### macOS (使用Homebrew)
```bash
# 安装Neo4j
brew install neo4j

# 启动服务
brew services start neo4j

# 或手动启动
neo4j start
```

#### Ubuntu/Debian
```bash
# 添加Neo4j仓库
wget -O - https://debian.neo4j.com/neotechnology.gpg.key | sudo apt-key add -
echo 'deb https://debian.neo4j.com stable latest' | sudo tee /etc/apt/sources.list.d/neo4j.list

# 更新包列表
sudo apt update

# 安装指定版本
sudo apt install neo4j=1:5.13.0

# 启动服务
sudo systemctl enable neo4j
sudo systemctl start neo4j
```

#### CentOS/RHEL
```bash
# 添加Neo4j仓库
sudo rpm --import https://debian.neo4j.com/neotechnology.gpg.key
echo '[neo4j]
name=Neo4j RPM Repository
baseurl=https://yum.neo4j.com/stable
enabled=1
gpgcheck=1' | sudo tee /etc/yum.repos.d/neo4j.repo

# 安装Neo4j
sudo yum install neo4j-5.13.0

# 启动服务
sudo systemctl enable neo4j
sudo systemctl start neo4j
```

#### Windows
1. 访问 [Neo4j下载中心](https://neo4j.com/download-center/#community)
2. 选择 "Neo4j Community Server 5.13.0"
3. 下载ZIP文件
4. 解压到目标目录（如 `C:\neo4j`）
5. 以管理员身份打开命令提示符
6. 进入Neo4j目录：`cd C:\neo4j\bin`
7. 安装服务：`neo4j.bat install-service`
8. 启动服务：`neo4j.bat start`

## ⚙️ 配置设置

### 1. 设置初始密码
首次启动后，需要设置密码：

#### 通过浏览器
1. 打开 http://localhost:7474
2. 默认用户名：`neo4j`
3. 默认密码：`neo4j`
4. 系统会要求设置新密码，建议设置为：`password123`

#### 通过命令行
```bash
# 设置密码
neo4j-admin dbms set-initial-password password123
```

### 2. 配置文件调整
编辑 `neo4j.conf` 文件（通常在 `/etc/neo4j/` 或安装目录的 `conf/` 下）：

```conf
# 基本配置
dbms.default_database=neo4j
dbms.default_listen_address=0.0.0.0

# 内存设置（适合开发环境）
dbms.memory.heap.initial_size=512m
dbms.memory.heap.max_size=1G
dbms.memory.pagecache.size=512m

# 连接器配置
dbms.connector.bolt.enabled=true
dbms.connector.bolt.listen_address=:7687
dbms.connector.http.enabled=true
dbms.connector.http.listen_address=:7474

# 安全设置（开发环境）
dbms.security.auth_enabled=true
```

### 3. 项目配置更新
确保项目的 `.env` 文件配置正确：

```env
# Neo4j配置（本地安装）
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password123
```

## 🔧 验证安装

### 1. 检查服务状态
```bash
# Linux/macOS
sudo systemctl status neo4j
# 或
neo4j status

# Windows
sc query Neo4j
```

### 2. 检查端口
```bash
# 检查Bolt端口（7687）
netstat -tulpn | grep 7687

# 检查HTTP端口（7474）
netstat -tulpn | grep 7474

# 或使用telnet测试
telnet localhost 7687
telnet localhost 7474
```

### 3. 浏览器访问
打开浏览器访问：http://localhost:7474
- 用户名：`neo4j`
- 密码：`password123`

### 4. Python连接测试
```python
from neo4j import GraphDatabase

# 测试连接
driver = GraphDatabase.driver("bolt://localhost:7687", auth=("neo4j", "password123"))
try:
    with driver.session() as session:
        result = session.run("RETURN 'Hello Neo4j!' as message")
        print(result.single()["message"])
    print("✅ Neo4j连接成功！")
except Exception as e:
    print(f"❌ 连接失败: {e}")
finally:
    driver.close()
```

## 🚀 启动和停止

### Neo4j Desktop
- **启动**: 在Desktop界面中点击数据库的 "Start" 按钮
- **停止**: 点击 "Stop" 按钮

### 命令行方式
```bash
# Linux/macOS
sudo systemctl start neo4j    # 启动
sudo systemctl stop neo4j     # 停止
sudo systemctl restart neo4j  # 重启

# 或使用neo4j命令
neo4j start
neo4j stop
neo4j restart

# Windows
net start Neo4j     # 启动
net stop Neo4j      # 停止
```

## 🐛 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
lsof -i :7687
lsof -i :7474

# 杀死占用进程
kill -9 <PID>
```

#### 2. 权限问题（Linux/macOS）
```bash
# 检查Neo4j用户权限
sudo chown -R neo4j:neo4j /var/lib/neo4j
sudo chown -R neo4j:neo4j /var/log/neo4j
```

#### 3. 内存不足
```bash
# 调整内存设置（在neo4j.conf中）
dbms.memory.heap.max_size=512m
dbms.memory.pagecache.size=256m
```

#### 4. Java版本问题
```bash
# 检查Java版本（需要Java 17+）
java -version

# 安装Java 17
# Ubuntu/Debian
sudo apt install openjdk-17-jdk

# macOS
brew install openjdk@17

# CentOS/RHEL
sudo yum install java-17-openjdk
```

#### 5. 连接被拒绝
```bash
# 检查防火墙设置
# Ubuntu/Debian
sudo ufw allow 7687
sudo ufw allow 7474

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=7687/tcp
sudo firewall-cmd --permanent --add-port=7474/tcp
sudo firewall-cmd --reload
```

## 💡 开发建议

### 1. 数据管理
```cypher
-- 清空数据库（开发测试时）
MATCH (n) DETACH DELETE n;

-- 查看数据库信息
CALL dbms.components();

-- 查看所有节点和关系
MATCH (n) RETURN n LIMIT 25;
```

### 2. 性能监控
```cypher
-- 查看运行中的查询
CALL dbms.listQueries();

-- 查看数据库统计
CALL dbms.queryJmx("org.neo4j:instance=kernel#0,name=Store file sizes");
```

### 3. 备份数据
```bash
# 停止Neo4j服务
neo4j stop

# 备份数据目录
cp -r /var/lib/neo4j/data /backup/neo4j-backup-$(date +%Y%m%d)

# 重启服务
neo4j start
```

## 🔄 从Docker迁移

如果之前使用Docker，现在想切换到本地安装：

1. **导出Docker数据**：
```bash
# 导出数据
docker exec neo4j-kg-mvp neo4j-admin database dump neo4j --to-path=/var/lib/neo4j/dumps
docker cp neo4j-kg-mvp:/var/lib/neo4j/dumps/neo4j.dump ./neo4j-backup.dump
```

2. **导入到本地Neo4j**：
```bash
# 停止本地Neo4j
neo4j stop

# 导入数据
neo4j-admin database load neo4j --from-path=. --overwrite-destination=true

# 启动Neo4j
neo4j start
```

## 📞 获取帮助

如果遇到安装问题：
1. 查看Neo4j日志：`/var/log/neo4j/neo4j.log`
2. 检查系统要求：Java 17+，至少2GB内存
3. 参考官方文档：https://neo4j.com/docs/
4. 社区支持：https://community.neo4j.com/

通过本地安装Neo4j，您可以获得更好的开发体验，无需依赖Docker环境！
