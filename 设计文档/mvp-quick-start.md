# 知识图谱MVP - 5分钟快速启动指南

## 🎯 目标
在5分钟内启动一个可用的知识图谱系统，适合完全的小白开发者。

## 📋 前置要求
- Python 3.8+
- Neo4j数据库（本地安装或Docker）
- 网络连接（下载依赖）
- uv包管理器（脚本会自动安装）

## 🚀 一键启动（推荐）

### 步骤1：下载项目创建脚本
```bash
# 下载项目创建脚本
curl -O https://raw.githubusercontent.com/your-repo/mvp-project-setup.py
# 或者手动下载 mvp-project-setup.py 文件
```

### 步骤2：运行项目创建脚本
```bash
python mvp-project-setup.py
```

### 步骤3：进入项目目录
```bash
cd knowledge-graph-mvp
```

### 步骤4：一键启动（Linux/Mac）
```bash
# 给脚本执行权限
chmod +x quick-start.sh

# 运行启动脚本
./quick-start.sh
```

### 步骤4：一键启动（Windows）
```batch
# 双击运行
quick-start.bat
```

## 📝 手动启动（详细步骤）

如果一键启动失败，可以按以下步骤手动启动：

### 1. 创建项目
```bash
# 运行项目创建脚本
python mvp-project-setup.py
cd knowledge-graph-mvp
```

### 2. 安装uv和依赖
```bash
# 安装uv包管理器（如果未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh  # Linux/Mac
# Windows: powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 配置阿里镜像源（可选，提升下载速度）
export UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
export UV_EXTRA_INDEX_URL=https://pypi.org/simple/

# 同步依赖（自动创建虚拟环境）
uv sync
```

### 3. 安装和启动Neo4j

#### 方式A：本地安装（推荐）
```bash
# 1. 下载Neo4j Desktop
# 访问：https://neo4j.com/download/
# 下载并安装Neo4j Desktop

# 2. 创建数据库
# - 打开Neo4j Desktop
# - 创建新项目和数据库
# - 版本选择：5.13.0
# - 密码设置：password123
# - 点击Start启动数据库

# 3. 验证连接
curl http://localhost:7474
```

#### 方式B：使用Docker（可选）
```bash
# 如果你更喜欢Docker
docker-compose up -d neo4j

# 等待启动完成（约30秒）
echo "等待Neo4j启动..."
sleep 30

# 验证Neo4j运行
curl http://localhost:7474
```

### 4. 配置LLM服务（可选）

#### 选项A：使用在线LLM（推荐）
```bash
# 无需安装，系统默认使用ModelScope等在线服务
# 在前端界面可以选择不同的LLM提供商
echo "✅ 将使用在线LLM服务，无需额外配置"
```

#### 选项B：安装本地Ollama
```bash
# Linux/Mac
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve &
ollama pull qwen3:1.7b

# Windows
# 1. 访问 https://ollama.ai/download
# 2. 下载Windows安装包
# 3. 安装后运行：ollama pull qwen3:1.7b
```

### 5. 启动应用服务

#### 方式1：一键启动（推荐）
```bash
cd knowledge-graph-mvp
./start.sh  # Linux/Mac
# 或 start.bat  # Windows
```

#### 方式2：手动启动
```bash
# 启动后端API（新终端）
cd knowledge-graph-mvp/src
uv run uvicorn main:app --reload --port 8000

# 启动前端界面（再开一个终端）
cd knowledge-graph-mvp/frontend
uv run streamlit run app.py --server.port 8501
```

## 🌐 访问应用

启动成功后，打开浏览器访问：

- **前端界面**: http://localhost:8501
- **API文档**: http://localhost:8000/docs
- **Neo4j浏览器**: http://localhost:7474
  - 用户名: `neo4j`
  - 密码: `password123`

## 🧪 快速测试

### 1. 测试文本提取
在前端界面：
1. 选择"文档上传"
2. 在文本框中输入：
```
张三是一名软件工程师，在北京工作。
他的同事李四是产品经理。
张三和李四经常合作开发新产品。
```
3. 点击"提取知识图谱"

### 2. 查看图谱可视化
1. 切换到"图谱可视化"
2. 点击"加载图谱"
3. 查看生成的知识图谱

### 3. 测试智能查询
1. 切换到"智能查询"
2. 输入问题："张三是做什么工作的？"
3. 点击"查询"

## 🔧 故障排除

### 问题1：端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :8000
netstat -tulpn | grep :8501

# 杀死占用进程
kill -9 <PID>
```

### 问题2：Docker启动失败
```bash
# 检查Docker状态
docker --version
docker ps

# 重启Docker服务
sudo systemctl restart docker  # Linux
# 或重启Docker Desktop应用
```

### 问题3：Neo4j连接失败
```bash
# 检查Neo4j容器状态
docker ps | grep neo4j

# 查看Neo4j日志
docker logs neo4j-kg-mvp

# 重启Neo4j
docker-compose restart neo4j
```

### 问题4：Ollama连接失败
```bash
# 检查Ollama进程
ps aux | grep ollama

# 重启Ollama
ollama serve

# 检查模型
ollama list
```

### 问题5：Python依赖安装失败
```bash
# 清理uv缓存
uv cache clean

# 重新同步依赖
uv sync --reinstall

# 如果仍有问题，尝试不使用缓存
uv sync --no-cache

# 检查uv版本
uv --version
```

## 📱 系统要求

### 最低配置
- CPU: 2核心
- 内存: 4GB
- 硬盘: 10GB可用空间
- 网络: 稳定的互联网连接

### 推荐配置
- CPU: 4核心+
- 内存: 8GB+
- 硬盘: 20GB+可用空间
- GPU: 可选（加速LLM推理）

## 🎓 新手提示

### 1. 第一次使用建议
- 先用简单的中文文本测试
- 文本长度控制在500字以内
- 确保文本包含明确的实体和关系

### 2. 常见概念解释
- **实体**: 文本中的人物、地点、组织等
- **关系**: 实体之间的连接，如"工作于"、"位于"等
- **知识图谱**: 实体和关系组成的网络结构

### 3. 性能优化建议
- 首次启动较慢，后续会更快
- 大文本建议分段处理
- 定期清理测试数据

## 🔄 停止服务

### 优雅停止
```bash
# 停止Streamlit (Ctrl+C)
# 停止FastAPI (Ctrl+C)
# 停止Neo4j
docker-compose down
# 停止Ollama
pkill ollama
```

### 完全清理
```bash
# 删除Neo4j数据
docker-compose down -v

# 删除uv创建的虚拟环境
uv clean

# 删除项目（可选）
cd ..
rm -rf knowledge-graph-mvp
```

## 📞 获取帮助

如果遇到问题：
1. 检查所有服务是否正常运行
2. 查看终端错误信息
3. 检查防火墙和端口设置
4. 参考详细文档：`mvp-implementation-guide.md`

## 🎉 成功标志

如果看到以下内容，说明启动成功：
- ✅ 前端界面正常显示
- ✅ 能够上传文本并提取实体
- ✅ 图谱可视化正常工作
- ✅ 智能查询有响应

恭喜！您已经成功启动了知识图谱MVP系统！🎊
