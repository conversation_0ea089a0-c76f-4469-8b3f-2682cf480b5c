# 知识图谱构建系统设计与开发文档

## 概述

本文档详细描述了知识图谱构建系统的架构、组件、实现方法以及开发指南。该系统旨在将非结构化文本转换为结构化的知识图谱，并支持基于图的检索增强生成(Graph RAG)。系统采用模块化设计，使用大语言模型(LLM)进行实体和关系提取，并使用图数据库存储和查询知识图谱。

本文档面向开发人员和技术决策者，提供了系统的详细设计和实现指南，包括代码示例、配置说明和开发最佳实践。

## 架构

系统采用双层分离架构，支持独立部署和LangGraph框架集成：

```
┌─────────────────────────────────────────────────────────────┐
│                    LangGraph集成层                           │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ KnowledgeGraph  │  │ LangGraph       │  │ LangChain    │ │
│  │ Agent           │  │ Workflow        │  │ Tools        │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────┐
│                      应用层 (Application Layer)              │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Web界面       │  │   CLI工具        │  │  API服务     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────┐
│                      核心功能层 (Core Layer)                 │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ 本体论管理      │  │ 知识图谱构建     │  │ GraphRAG服务 │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ 多模态处理      │  │ 社区检测        │  │ 推理引擎     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────┐
│                      服务层 (Service Layer)                  │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ 文本处理服务    │  │ LLM服务         │  │ 图数据库服务  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据层 (Data Layer)                     │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ 文档存储        │  │ 向量数据库      │  │ 图数据库     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件说明

1. **LangGraph集成层**：提供框架集成支持
   - KnowledgeGraph Agent：LangGraph兼容的智能体实现
   - LangGraph Workflow：基于状态图的工作流编排
   - LangChain Tools：工具包装接口，支持工具链调用

2. **应用层**：提供用户交互界面和API接口
   - Web界面：基于Streamlit构建的可视化操作界面
   - CLI工具：命令行工具，用于批处理和自动化
   - API服务：RESTful API，供外部系统集成

3. **核心功能层**：实现框架无关的核心业务逻辑
   - 本体论管理：创建、编辑和管理知识图谱的本体论
   - 知识图谱构建：从文本中提取实体和关系，构建知识图谱
   - GraphRAG服务：基于知识图谱的检索增强生成服务
   - 多模态处理：处理图像、表格、公式等多种模态内容
   - 社区检测：发现知识图谱中的社区结构
   - 推理引擎：提供知识推理和预测能力

4. **服务层**：提供基础服务支持
   - 文本处理服务：文本分块、清洗和预处理
   - LLM服务：与大语言模型交互，进行实体关系提取
   - 图数据库服务：与Neo4j等图数据库交互

5. **数据层**：负责数据存储和管理
   - 文档存储：原始文档和处理后的文档块
   - 向量数据库：存储文本嵌入向量，用于相似性搜索
   - 图数据库：存储知识图谱的节点和边

## 组件和接口

### 1. 本体论管理器 (OntologyManager)

**职责**：管理知识图谱的本体论定义，包括实体类型和关系类型。

**主要接口**：

```python
class OntologyManager:
    def create_ontology(self, labels: List[Union[str, Dict]], relationships: List[str]) -> Ontology:
        """创建新的本体论"""
        pass
        
    def validate_ontology(self, ontology: Ontology) -> bool:
        """验证本体论的有效性"""
        pass
        
    def save_ontology(self, ontology: Ontology, path: str) -> None:
        """保存本体论到文件"""
        pass
        
    def load_ontology(self, path: str) -> Ontology:
        """从文件加载本体论"""
        pass
```

**数据模型**：

```python
class Ontology(BaseModel):
    """本体论模型，定义知识图谱的结构"""
    labels: List[Union[str, Dict]]  # 实体类型列表
    relationships: List[str]        # 关系类型列表
```

### 2. 多模态文档处理器 (MultiModalDocumentProcessor)

**职责**：处理多模态文档，包括解析、分块、内容提取和元数据添加。

**主要接口**：

```python
class MultiModalDocumentProcessor:
    def __init__(self, llm_client: BaseLLMClient = None, vision_client: BaseLLMClient = None):
        self.llm_client = llm_client
        self.vision_client = vision_client
        self.document_parser = DocumentParser()
        self.text_chunker = TextChunker()
        
    def parse_document(self, file_path: str, parse_method: str = "auto") -> ParsedContent:
        """解析多模态文档，提取各种内容类型"""
        pass
        
    def process_multimodal_document(self, file_path: str, metadata: Dict = None) -> MultiModalDocument:
        """处理多模态文档并创建文档对象"""
        pass
        
    def extract_modal_content(self, parsed_content: ParsedContent) -> Dict[str, List]:
        """从解析内容中提取不同模态的内容"""
        pass
        
    def generate_multimodal_summary(self, document: MultiModalDocument) -> str:
        """为多模态文档生成综合摘要"""
        pass
        
    def chunk_multimodal_content(self, content: Any, content_type: str) -> List[Any]:
        """对不同模态的内容进行分块处理"""
        pass
```

**数据模型**：

```python
class MultiModalDocument(BaseModel):
    """多模态文档模型，包含各种类型的内容和元数据"""
    text_content: str                    # 文本内容
    images: List[ImageContent] = []      # 图像内容列表
    tables: List[TableContent] = []      # 表格内容列表
    equations: List[EquationContent] = [] # 数学公式列表
    metadata: Dict                       # 元数据，如来源、时间戳等
    document_structure: DocumentStructure # 文档结构信息

class ImageContent(BaseModel):
    """图像内容模型"""
    image_path: str
    caption: Optional[str] = None
    description: Optional[str] = None
    position: Optional[Dict] = None      # 在文档中的位置
    
class TableContent(BaseModel):
    """表格内容模型"""
    table_data: str                      # 表格的文本表示
    caption: Optional[str] = None
    headers: List[str] = []
    rows: List[List[str]] = []
    position: Optional[Dict] = None

class EquationContent(BaseModel):
    """数学公式内容模型"""
    latex_content: str
    description: Optional[str] = None
    position: Optional[Dict] = None

class DocumentStructure(BaseModel):
    """文档结构模型"""
    title: Optional[str] = None
    sections: List[Section] = []
    page_count: int = 0
    
class Section(BaseModel):
    """文档章节模型"""
    title: str
    level: int                           # 标题级别
    content: str
    subsections: List['Section'] = []
```

### 3. LLM客户端 (LLMClient)

**职责**：与大语言模型交互，进行实体和关系提取。支持企业内部部署的开源模型和第三方API服务。

**主要接口**：

```python
class BaseLLMClient(ABC):
    """LLM客户端基类"""
    
    @abstractmethod
    def generate(self, user_message: str, system_message: str = None) -> str:
        """调用LLM生成文本"""
        pass

class OpenAICompatibleClient(BaseLLMClient):
    """OpenAI兼容API客户端，支持各种部署方式"""
    
    def __init__(self, 
                 model: str = "qwen2.5-7b-instruct", 
                 base_url: str = "http://localhost:8000/v1",
                 api_key: str = "EMPTY",
                 temperature: float = 0.1, 
                 top_p: float = 0.5):
        self.model = model
        self.base_url = base_url
        self.api_key = api_key
        self.temperature = temperature
        self.top_p = top_p
        
    def generate(self, user_message: str, system_message: str = None) -> str:
        """调用OpenAI兼容API生成文本"""
        pass

class VLLMClient(OpenAICompatibleClient):
    """vLLM部署的模型客户端"""
    
    def __init__(self, model: str = "qwen2.5-7b-instruct", host: str = "localhost", port: int = 8000):
        super().__init__(
            model=model,
            base_url=f"http://{host}:{port}/v1",
            api_key="EMPTY"
        )

class OllamaClient(OpenAICompatibleClient):
    """Ollama部署的模型客户端"""
    
    def __init__(self, model: str = "qwen2.5:7b", host: str = "localhost", port: int = 11434):
        super().__init__(
            model=model,
            base_url=f"http://{host}:{port}/v1",
            api_key="ollama"
        )

class ThirdPartyClient(OpenAICompatibleClient):
    """第三方API服务客户端（用于开发调试）"""
    
    def __init__(self, provider: str = "openai", model: str = "gpt-3.5-turbo", api_key: str = None):
        provider_configs = {
            "openai": {"base_url": "https://api.openai.com/v1", "model": "gpt-3.5-turbo"},
            "azure": {"base_url": "https://your-resource.openai.azure.com/", "model": "gpt-35-turbo"},
            "zhipu": {"base_url": "https://open.bigmodel.cn/api/paas/v4/", "model": "glm-4"},
            "baidu": {"base_url": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/", "model": "ernie-3.5-8k"},
        }
        
        config = provider_configs.get(provider, provider_configs["openai"])
        super().__init__(
            model=model or config["model"],
            base_url=config["base_url"],
            api_key=api_key or os.environ.get(f"{provider.upper()}_API_KEY")
        )
```

### 4. 多模态图构建器 (MultiModalGraphMaker)

**职责**：使用LLM和视觉模型从多模态文档中提取实体和关系，构建知识图谱。

**主要接口**：

```python
class MultiModalGraphMaker(GraphMaker):
    def __init__(self, ontology: Ontology, llm_client: BaseLLMClient, 
                 vision_client: BaseLLMClient = None, verbose: bool = False):
        super().__init__(ontology, llm_client, verbose)
        self.vision_client = vision_client
        self.document_parser = DocumentParser()
        self.modal_processors = {
            'image': ImageModalProcessor(vision_client),
            'table': TableModalProcessor(llm_client),
            'equation': EquationModalProcessor(llm_client),
            'generic': GenericModalProcessor(llm_client)
        }
        
    def from_multimodal_document(self, document: MultiModalDocument) -> List[Edge]:
        """从多模态文档中提取知识图谱"""
        pass
        
    def from_parsed_content(self, parsed_content: ParsedContent) -> List[Edge]:
        """从解析后的多模态内容中提取知识图谱"""
        pass
        
    def _extract_multimodal_relations(self, modal_content: Dict) -> List[Edge]:
        """提取跨模态关系"""
        pass
        
    def _process_modal_content(self, content: Any, content_type: str) -> Tuple[str, Dict]:
        """处理特定模态的内容"""
        pass
```

**数据模型**：

```python
class Node(BaseModel):
    """知识图谱中的节点，表示实体"""
    label: str  # 实体类型
    name: str   # 实体名称

class Edge(BaseModel):
    """知识图谱中的边，表示关系"""
    node_1: Node                # 起始节点
    node_2: Node                # 终止节点
    relationship: str           # 关系类型
    metadata: Dict = {}         # 元数据
    order: Optional[int] = None # 顺序（可选）
```

### 5. 图数据库模型 (Neo4jGraphModel)

**职责**：将知识图谱保存到Neo4j图数据库，并提供查询接口。

**主要接口**：

```python
class Neo4jGraphModel:
    def __init__(self, edges: List[Edge], create_indices: bool = True):
        self.edges = edges
        self.create_indices = create_indices
        
    def save(self) -> int:
        """将知识图谱保存到Neo4j数据库"""
        pass
        
    def query(self, cypher_query: str) -> List[Dict]:
        """执行Cypher查询"""
        pass
        
    def get_node_by_name(self, name: str) -> List[Node]:
        """根据名称查找节点"""
        pass
        
    def get_related_nodes(self, node_name: str, relationship_type: str = None) -> List[Dict]:
        """查找与指定节点相关的节点"""
        pass
```

### 6. 社区检测器 (CommunityDetector)

**职责**：在知识图谱中检测社区结构，并生成层次化摘要。

**主要接口**：

```python
class CommunityDetector:
    def __init__(self, graph_model: Neo4jGraphModel, llm_client: BaseLLMClient):
        self.graph_model = graph_model
        self.llm_client = llm_client
        
    def detect_communities(self, algorithm: str = "leiden") -> Dict:
        """使用指定算法检测社区"""
        pass
        
    def generate_community_summary(self, community_id: str) -> str:
        """为社区生成摘要"""
        pass
        
    def build_hierarchical_structure(self) -> Dict:
        """构建社区的层次结构"""
        pass
```

### 7. GraphRAG服务 (GraphRAGService)

**职责**：提供基于知识图谱的检索增强生成服务。

**主要接口**：

```python
class GraphRAGService:
    def __init__(self, graph_model: Neo4jGraphModel, llm_client: BaseLLMClient, 
                 community_detector: CommunityDetector = None):
        self.graph_model = graph_model
        self.llm_client = llm_client
        self.community_detector = community_detector
        
    def local_graph_rag(self, query: str) -> str:
        """基于局部图检索的RAG"""
        pass
        
    def global_graph_rag(self, query: str) -> str:
        """基于全局图和社区结构的RAG"""
        pass
        
    def hybrid_graph_rag(self, query: str) -> str:
        """混合局部和全局图检索的RAG"""
        pass
        
    def _extract_query_entities(self, query: str) -> List[str]:
        """从查询中提取实体"""
        pass
        
    def _retrieve_relevant_subgraph(self, entities: List[str]) -> List[Edge]:
        """检索与查询实体相关的子图"""
        pass
```

### 8. 文档解析器 (DocumentParser)

**职责**：集成MinerU等工具，实现高保真的文档解析和内容提取。

**主要接口**：

```python
class DocumentParser:
    def __init__(self, parse_backend: str = "mineru"):
        self.parse_backend = parse_backend
        self.mineru_client = MinerUClient() if parse_backend == "mineru" else None
        
    def parse_document(self, file_path: str, parse_method: str = "auto") -> ParsedContent:
        """解析文档并提取多模态内容"""
        pass
        
    def parse_pdf(self, file_path: str) -> ParsedContent:
        """解析PDF文档"""
        pass
        
    def parse_office_document(self, file_path: str) -> ParsedContent:
        """解析Office文档（需要LibreOffice）"""
        pass
        
    def parse_image(self, file_path: str) -> ParsedContent:
        """解析图像文件"""
        pass
        
    def extract_document_structure(self, parsed_content: Any) -> DocumentStructure:
        """提取文档结构信息"""
        pass

class MinerUClient:
    """MinerU 2.0客户端封装"""
    def __init__(self):
        self.check_installation()
        
    def parse_with_mineru(self, file_path: str, output_dir: str, 
                         parse_method: str = "auto") -> Dict:
        """使用MinerU解析文档"""
        pass
        
    def check_installation(self) -> bool:
        """检查MinerU安装状态"""
        pass
```

### 9. 模态处理器 (ModalProcessors)

**职责**：处理不同模态的内容，提取语义信息和实体关系。

**主要接口**：

```python
class BaseModalProcessor(ABC):
    """模态处理器基类"""
    def __init__(self, llm_client: BaseLLMClient):
        self.llm_client = llm_client
        
    @abstractmethod
    async def process_content(self, content: Any, context: Dict = None) -> ProcessedContent:
        """处理特定模态的内容"""
        pass

class ImageModalProcessor(BaseModalProcessor):
    """图像内容处理器"""
    def __init__(self, vision_client: BaseLLMClient):
        super().__init__(vision_client)
        
    async def process_content(self, image_content: ImageContent, 
                            context: Dict = None) -> ProcessedContent:
        """处理图像内容，生成描述和提取实体"""
        pass
        
    async def generate_image_description(self, image_path: str) -> str:
        """生成图像描述"""
        pass
        
    async def extract_image_entities(self, image_description: str) -> List[Dict]:
        """从图像描述中提取实体"""
        pass

class TableModalProcessor(BaseModalProcessor):
    """表格内容处理器"""
    async def process_content(self, table_content: TableContent, 
                            context: Dict = None) -> ProcessedContent:
        """处理表格内容，提取数据关系"""
        pass
        
    async def analyze_table_structure(self, table_data: str) -> Dict:
        """分析表格结构"""
        pass
        
    async def extract_table_relationships(self, table_analysis: Dict) -> List[Dict]:
        """提取表格中的关系"""
        pass

class EquationModalProcessor(BaseModalProcessor):
    """数学公式处理器"""
    async def process_content(self, equation_content: EquationContent, 
                            context: Dict = None) -> ProcessedContent:
        """处理数学公式，建立概念关联"""
        pass
        
    async def parse_latex_equation(self, latex_content: str) -> Dict:
        """解析LaTeX公式"""
        pass
        
    async def extract_mathematical_concepts(self, equation_analysis: Dict) -> List[Dict]:
        """提取数学概念"""
        pass
```

### 10. LangGraph集成组件

**职责**：提供LangGraph框架兼容的智能体接口，支持作为子智能体集成到更大的智能体系统中。

#### 10.1 知识图谱核心 (KnowledgeGraphCore)

**职责**：封装所有核心功能，提供框架无关的接口。

```python
class KnowledgeGraphCore:
    """知识图谱核心功能，不依赖任何Agent框架"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.ontology_manager = OntologyManager()
        self.document_processor = MultiModalDocumentProcessor()
        self.graph_maker = MultiModalGraphMaker()
        self.graph_model = Neo4jGraphModel()
        self.graph_rag_service = GraphRAGService()
        
    def process_documents(self, documents: List[str], ontology: Ontology = None) -> List[Document]:
        """处理文档列表"""
        processed_docs = []
        for doc_text in documents:
            doc = self.document_processor.create_document(doc_text)
            processed_docs.append(doc)
        return processed_docs
    
    def build_knowledge_graph(self, documents: List[Document], ontology: Ontology = None) -> Dict:
        """构建知识图谱"""
        if ontology is None:
            ontology = self.ontology_manager.create_default_ontology()
        
        all_edges = []
        for doc in documents:
            edges = self.graph_maker.from_document(doc)
            all_edges.extend(edges)
        
        # 保存到图数据库
        graph_model = Neo4jGraphModel(all_edges)
        node_count = graph_model.save()
        
        return {
            "edges": all_edges,
            "node_count": node_count,
            "ontology": ontology
        }
    
    def query_knowledge_graph(self, query: str, method: str = "hybrid") -> str:
        """查询知识图谱"""
        if method == "local":
            return self.graph_rag_service.local_graph_rag(query)
        elif method == "global":
            return self.graph_rag_service.global_graph_rag(query)
        else:
            return self.graph_rag_service.hybrid_graph_rag(query)
```

#### 10.2 LangGraph状态定义

```python
from typing import TypedDict, List, Dict, Any
from langchain_core.messages import BaseMessage

class KnowledgeGraphState(TypedDict):
    """LangGraph工作流状态定义"""
    # 输入消息
    messages: List[BaseMessage]
    
    # 文档处理
    documents: List[str]
    processed_documents: List[Dict]
    
    # 本体论
    ontology: Dict
    
    # 知识图谱
    graph_data: Dict
    
    # 查询结果
    query_result: str
    query_method: str
    
    # 错误处理
    error: str
    
    # 元数据
    metadata: Dict[str, Any]
```

#### 10.3 LangGraph智能体 (KnowledgeGraphAgent)

```python
from langgraph import StateGraph, END
from langgraph.prebuilt import ToolExecutor

class KnowledgeGraphAgent:
    """LangGraph兼容的知识图谱智能体"""
    
    def __init__(self, config: Dict = None):
        self.core = KnowledgeGraphCore(config)
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """构建LangGraph工作流"""
        workflow = StateGraph(KnowledgeGraphState)
        
        # 添加工作流节点
        workflow.add_node("process_documents", self._process_documents_node)
        workflow.add_node("build_graph", self._build_graph_node)
        workflow.add_node("query_graph", self._query_graph_node)
        workflow.add_node("handle_error", self._handle_error_node)
        
        # 定义工作流边
        workflow.set_entry_point("process_documents")
        
        # 条件边：根据是否有错误决定下一步
        workflow.add_conditional_edges(
            "process_documents",
            self._should_continue,
            {
                "continue": "build_graph",
                "error": "handle_error"
            }
        )
        
        workflow.add_conditional_edges(
            "build_graph",
            self._should_continue,
            {
                "continue": "query_graph",
                "error": "handle_error"
            }
        )
        
        workflow.add_edge("query_graph", END)
        workflow.add_edge("handle_error", END)
        
        return workflow.compile()
    
    def _process_documents_node(self, state: KnowledgeGraphState) -> KnowledgeGraphState:
        """文档处理节点"""
        try:
            documents = state.get("documents", [])
            if not documents:
                # 如果没有文档，从消息中提取
                messages = state.get("messages", [])
                if messages:
                    documents = [msg.content for msg in messages if hasattr(msg, 'content')]
            
            processed_docs = self.core.process_documents(documents)
            
            state["processed_documents"] = [
                {
                    "text": doc.text,
                    "metadata": doc.metadata
                }
                for doc in processed_docs
            ]
            
            return state
            
        except Exception as e:
            state["error"] = f"文档处理错误: {str(e)}"
            return state
    
    def _build_graph_node(self, state: KnowledgeGraphState) -> KnowledgeGraphState:
        """知识图谱构建节点"""
        try:
            processed_docs = state.get("processed_documents", [])
            if not processed_docs:
                state["error"] = "没有可处理的文档"
                return state
            
            # 重建Document对象
            documents = []
            for doc_data in processed_docs:
                doc = Document(text=doc_data["text"], metadata=doc_data["metadata"])
                documents.append(doc)
            
            # 构建知识图谱
            graph_data = self.core.build_knowledge_graph(documents)
            state["graph_data"] = graph_data
            
            return state
            
        except Exception as e:
            state["error"] = f"图谱构建错误: {str(e)}"
            return state
    
    def _query_graph_node(self, state: KnowledgeGraphState) -> KnowledgeGraphState:
        """知识图谱查询节点"""
        try:
            messages = state.get("messages", [])
            if not messages:
                state["query_result"] = "知识图谱构建完成，但没有查询问题"
                return state
            
            # 获取最后一条消息作为查询
            query = messages[-1].content if hasattr(messages[-1], 'content') else str(messages[-1])
            query_method = state.get("query_method", "hybrid")
            
            result = self.core.query_knowledge_graph(query, query_method)
            state["query_result"] = result
            
            return state
            
        except Exception as e:
            state["error"] = f"图谱查询错误: {str(e)}"
            return state
    
    def _handle_error_node(self, state: KnowledgeGraphState) -> KnowledgeGraphState:
        """错误处理节点"""
        error = state.get("error", "未知错误")
        state["query_result"] = f"处理过程中出现错误: {error}"
        return state
    
    def _should_continue(self, state: KnowledgeGraphState) -> str:
        """判断是否继续执行"""
        if state.get("error"):
            return "error"
        return "continue"
    
    def invoke(self, input_data: Dict) -> Dict:
        """调用智能体"""
        return self.workflow.invoke(input_data)
    
    async def ainvoke(self, input_data: Dict) -> Dict:
        """异步调用智能体"""
        return await self.workflow.ainvoke(input_data)
```

#### 10.4 LangChain工具包装

```python
from langchain_core.tools import BaseTool
from typing import Optional, Type
from pydantic import BaseModel, Field

class KnowledgeGraphInput(BaseModel):
    """知识图谱工具输入模型"""
    query: str = Field(description="要查询的问题")
    documents: Optional[List[str]] = Field(default=None, description="要处理的文档列表")
    query_method: Optional[str] = Field(default="hybrid", description="查询方法: local, global, hybrid")

class KnowledgeGraphTool(BaseTool):
    """知识图谱LangChain工具"""
    name = "knowledge_graph"
    description = "构建和查询知识图谱。可以处理文档并回答基于知识图谱的问题。"
    args_schema: Type[BaseModel] = KnowledgeGraphInput
    
    def __init__(self, config: Dict = None):
        super().__init__()
        self.kg_agent = KnowledgeGraphAgent(config)
    
    def _run(self, query: str, documents: Optional[List[str]] = None, 
             query_method: str = "hybrid") -> str:
        """同步执行工具"""
        from langchain_core.messages import HumanMessage
        
        initial_state = {
            "messages": [HumanMessage(content=query)],
            "documents": documents or [],
            "query_method": query_method,
            "ontology": {},
            "graph_data": {},
            "query_result": "",
            "error": "",
            "metadata": {}
        }
        
        result = self.kg_agent.invoke(initial_state)
        return result.get("query_result", "无法生成结果")
    
    async def _arun(self, query: str, documents: Optional[List[str]] = None,
                    query_method: str = "hybrid") -> str:
        """异步执行工具"""
        from langchain_core.messages import HumanMessage
        
        initial_state = {
            "messages": [HumanMessage(content=query)],
            "documents": documents or [],
            "query_method": query_method,
            "ontology": {},
            "graph_data": {},
            "query_result": "",
            "error": "",
            "metadata": {}
        }
        
        result = await self.kg_agent.ainvoke(initial_state)
        return result.get("query_result", "无法生成结果")
```

#### 10.5 集成示例

```python
# 作为独立智能体使用
def standalone_usage():
    """独立使用示例"""
    kg_agent = KnowledgeGraphAgent()
    
    result = kg_agent.invoke({
        "messages": [HumanMessage(content="张三在哪家公司工作？")],
        "documents": ["张三是ABC公司的工程师。"],
        "query_method": "hybrid"
    })
    
    print(result["query_result"])

# 作为LangChain工具使用
def langchain_tool_usage():
    """LangChain工具使用示例"""
    from langchain.agents import initialize_agent, AgentType
    from langchain_openai import ChatOpenAI
    
    llm = ChatOpenAI()
    kg_tool = KnowledgeGraphTool()
    
    agent = initialize_agent(
        tools=[kg_tool],
        llm=llm,
        agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION
    )
    
    result = agent.run("根据提供的文档构建知识图谱并回答：张三在哪工作？")
    print(result)

# 作为LangGraph子智能体集成
def langgraph_subagent_usage():
    """LangGraph子智能体集成示例"""
    from langgraph import StateGraph
    
    class MasterState(TypedDict):
        messages: List[BaseMessage]
        kg_result: str
        final_result: str
    
    def knowledge_graph_subagent(state: MasterState) -> MasterState:
        """知识图谱子智能体节点"""
        kg_agent = KnowledgeGraphAgent()
        
        kg_state = {
            "messages": state["messages"],
            "documents": [],
            "query_method": "hybrid"
        }
        
        result = kg_agent.invoke(kg_state)
        state["kg_result"] = result["query_result"]
        return state
    
    def final_processing(state: MasterState) -> MasterState:
        """最终处理节点"""
        state["final_result"] = f"基于知识图谱的回答: {state['kg_result']}"
        return state
    
    # 构建主智能体工作流
    workflow = StateGraph(MasterState)
    workflow.add_node("kg_subagent", knowledge_graph_subagent)
    workflow.add_node("final_processing", final_processing)
    
    workflow.set_entry_point("kg_subagent")
    workflow.add_edge("kg_subagent", "final_processing")
    workflow.add_edge("final_processing", END)
    
    master_agent = workflow.compile()
    
    result = master_agent.invoke({
        "messages": [HumanMessage(content="分析公司组织结构")],
        "kg_result": "",
        "final_result": ""
    })
    
    print(result["final_result"])
```

## 数据模型

### 1. 知识图谱数据模型

知识图谱采用属性图模型，包含以下主要元素：

1. **节点(Nodes)**：表示实体，具有标签(label)和属性(properties)
   - 标签：表示实体类型，如Person、Place、Event等
   - 属性：表示实体的特征，如name、description等

2. **关系(Relationships)**：表示实体之间的连接，具有类型和属性
   - 类型：表示关系的性质，如works_at、located_in等
   - 属性：表示关系的特征，如start_date、end_date等

3. **属性(Properties)**：节点和关系上的键值对
   - 基本属性：name、id等
   - 元数据属性：source、timestamp、confidence等

### 2. Neo4j数据模型示例

```cypher
// 创建节点
CREATE (p:Person {name: "张三", id: "p001"})
CREATE (o:Organization {name: "ABC公司", id: "o001"})

// 创建关系
CREATE (p)-[:WORKS_AT {start_date: "2020-01-01", position: "工程师"}]->(o)
```

### 3. 文档数据模型

```python
document = Document(
    text="张三是ABC公司的一名工程师，他于2020年1月加入公司。",
    metadata={
        "source": "company_profile.txt",
        "page": 5,
        "timestamp": "2025-07-24T10:30:00",
        "summary": "描述了张三在ABC公司的工作情况"
    }
)
```

## 处理流程

### 1. 知识图谱构建流程

```mermaid
flowchart TD
    A[输入文本] --> B[文本分块]
    B --> C[创建文档对象]
    C --> D[生成文档摘要]
    D --> E[实体和关系提取]
    E --> F[构建知识图谱]
    F --> G[保存到Neo4j]
    G --> H[社区检测]
    H --> I[生成社区摘要]
```

1. **输入文本**：用户提供原始文本文档
2. **文本分块**：将文本分割成适当大小的块(800-1200 tokens)
3. **创建文档对象**：为每个文本块创建Document对象
4. **生成文档摘要**：使用LLM为每个文档生成摘要作为元数据
5. **实体和关系提取**：使用LLM根据本体论从文本中提取实体和关系
6. **构建知识图谱**：将提取的实体和关系组织成知识图谱
7. **保存到Neo4j**：将知识图谱保存到Neo4j数据库
8. **社区检测**：使用Leiden算法在知识图谱中检测社区结构
9. **生成社区摘要**：为每个社区生成描述性摘要

### 2. GraphRAG查询流程

```mermaid
flowchart TD
    A[用户查询] --> B{查询类型判断}
    B -->|局部查询| C[提取查询实体]
    B -->|全局查询| D[社区级别检索]
    C --> E[检索相关子图]
    D --> F[检索社区摘要]
    E --> G[生成回答]
    F --> G
    G --> H[返回结果]
```

1. **用户查询**：用户提出问题
2. **查询类型判断**：判断是局部查询还是全局查询
3. **提取查询实体**：从查询中提取关键实体
4. **社区级别检索**：检索相关社区及其摘要
5. **检索相关子图**：检索与查询实体相关的子图
6. **生成回答**：结合检索结果生成回答
7. **返回结果**：将回答返回给用户

## 技术选型

### 1. 编程语言和框架

- **主要语言**：Python 3.10+
- **Web框架**：FastAPI (API服务)、Streamlit (Web界面)
- **Agent框架**：LangGraph (智能体编排)、LangChain (工具集成)
- **数据处理**：Pandas、NumPy
- **异步处理**：asyncio

### 2. 大语言模型

**生产环境（企业内部部署）**：

- **vLLM部署**：Qwen2.5、DeepSeek-V2、ChatGLM4、Baichuan2等开源模型
- **Ollama部署**：Llama3、Mistral、Qwen等轻量化部署
- **统一接口**：所有模型通过OpenAI兼容API接口访问

**开发调试环境**：

- **第三方API**：OpenAI GPT系列、Azure OpenAI、智谱AI、百度文心一言、阿里通义千问等
- **兼容性**：支持任何OpenAI兼容的API服务

### 3. 图数据库

- **Neo4j**：主要的图数据库，用于存储和查询知识图谱
- **Neo4j Bloom**：用于知识图谱可视化

### 4. 向量数据库

- **Faiss**：用于高效的向量相似性搜索
- **Chroma**：用于存储和检索文本嵌入向量

### 5. 社区检测算法

- **Leiden算法**：用于高质量社区检测
- **Louvain算法**：作为备选社区检测算法

## 错误处理

### 1. LLM调用错误

- **重试机制**：对于临时性错误，实现指数退避重试
- **降级策略**：当首选模型不可用时，自动切换到备选模型
- **错误日志**：详细记录错误信息，便于调试

### 2. 图数据库错误

- **连接池管理**：维护数据库连接池，避免连接过多
- **事务管理**：使用事务确保数据一致性
- **备份策略**：定期备份图数据，防止数据丢失

### 3. 实体提取错误

- **验证机制**：验证提取的实体和关系是否符合本体论
- **人工审核**：对于低置信度的提取结果，标记为待审核
- **错误修正**：提供界面让用户修正错误的实体和关系

## 性能优化

### 1. 企业级性能优化框架

**PerformanceOptimizer类设计**：

```python
class PerformanceOptimizer:
    """企业级性能优化器"""

    def __init__(self):
        self.load_balancer = LoadBalancer()
        self.cache_manager = DistributedCacheManager()
        self.query_optimizer = GraphQueryOptimizer()
        self.resource_monitor = ResourceMonitor()
        self.auto_scaler = AutoScaler()

    def optimize_batch_processing(self, documents: List[Document]) -> List[Batch]:
        """智能批处理优化"""
        # 根据文档大小、复杂度和系统负载动态调整批次大小
        optimal_batch_size = self.calculate_optimal_batch_size(documents)

        # 智能分组：相似文档分到同一批次，提高缓存命中率
        batches = self.group_similar_documents(documents, optimal_batch_size)

        # 负载均衡：将批次分配到不同的处理节点
        return self.load_balancer.distribute_batches(batches)

    def scale_processing_nodes(self, load_metrics: Dict) -> None:
        """动态扩展处理节点"""
        if load_metrics['cpu_usage'] > 0.8 or load_metrics['memory_usage'] > 0.85:
            self.auto_scaler.scale_up()
        elif load_metrics['cpu_usage'] < 0.3 and load_metrics['memory_usage'] < 0.4:
            self.auto_scaler.scale_down()

    def optimize_llm_calls(self, requests: List[LLMRequest]) -> List[LLMRequest]:
        """优化LLM调用"""
        # 请求合并：将相似的请求合并为批量请求
        merged_requests = self.merge_similar_requests(requests)

        # 缓存检查：检查是否有缓存的结果
        cached_results = self.cache_manager.get_cached_results(merged_requests)

        # 只处理未缓存的请求
        uncached_requests = [req for req in merged_requests if req.id not in cached_results]

        return uncached_requests

class LoadBalancer:
    """负载均衡器"""

    def __init__(self):
        self.processing_nodes = []
        self.node_metrics = {}

    def distribute_batches(self, batches: List[Batch]) -> List[Batch]:
        """将批次分配到最优的处理节点"""
        for batch in batches:
            optimal_node = self.select_optimal_node()
            batch.assigned_node = optimal_node
            self.update_node_load(optimal_node, batch.estimated_load)
        return batches

    def select_optimal_node(self) -> str:
        """选择最优处理节点"""
        # 基于CPU、内存、网络等指标选择最优节点
        return min(self.processing_nodes,
                  key=lambda node: self.calculate_node_score(node))

class DistributedCacheManager:
    """分布式缓存管理器"""

    def __init__(self):
        self.redis_cluster = RedisCluster()
        self.local_cache = LRUCache(maxsize=10000)
        self.cache_policies = {}

    def get_cached_result(self, key: str, cache_level: str = 'auto') -> Optional[Any]:
        """多级缓存获取"""
        # L1: 本地缓存
        if cache_level in ['auto', 'local']:
            result = self.local_cache.get(key)
            if result is not None:
                return result

        # L2: 分布式缓存
        if cache_level in ['auto', 'distributed']:
            result = self.redis_cluster.get(key)
            if result is not None:
                # 回填本地缓存
                self.local_cache.set(key, result)
                return result

        return None

    def set_cached_result(self, key: str, value: Any, ttl: int = 3600):
        """设置缓存结果"""
        # 同时设置本地和分布式缓存
        self.local_cache.set(key, value)
        self.redis_cluster.setex(key, ttl, value)
```

### 2. 批处理优化

- **智能批处理**：根据文档复杂度和系统负载动态调整批次大小
- **并行处理**：支持多进程、多线程和异步处理
- **请求合并**：将相似的LLM请求合并为批量请求
- **延迟控制**：智能延迟控制，避免触发API速率限制

### 3. 图查询优化

- **查询计划优化**：智能查询计划生成和优化
- **多级缓存**：本地缓存+分布式缓存的多级缓存策略
- **索引策略**：基于查询模式的智能索引创建和维护
- **查询重写**：自动查询重写和优化

### 4. 资源管理

- **动态扩缩容**：基于负载指标的自动扩缩容
- **资源隔离**：不同租户的资源隔离和配额管理
- **内存优化**：智能内存管理和垃圾回收策略
- **连接池管理**：数据库连接池的智能管理

## 测试策略

### 1. 单元测试

- **组件测试**：测试各个组件的功能
- **模拟测试**：使用模拟对象测试依赖外部服务的组件
- **边界测试**：测试边界条件和异常情况

### 2. 集成测试

- **组件集成**：测试组件之间的交互
- **API测试**：测试API接口的功能和性能
- **数据流测试**：测试数据在系统中的流动

### 3. 性能测试

- **负载测试**：测试系统在高负载下的性能
- **耐久测试**：测试系统在长时间运行下的稳定性
- **扩展性测试**：测试系统的扩展能力

## 部署架构

### 1. 开发环境

- **本地开发**：使用Docker Compose在本地运行所有组件
- **测试数据**：使用小型测试数据集进行开发和测试
- **框架测试**：支持原生API和LangGraph接口的并行测试

### 2. 生产环境

- **容器化部署**：使用Docker和Kubernetes部署
- **微服务架构**：将系统拆分为多个微服务
- **双重接口**：同时提供原生API和LangGraph兼容接口
- **负载均衡**：使用负载均衡器分发请求
- **自动扩展**：根据负载自动扩展服务实例

### 3. 集成部署

- **子智能体模式**：作为LangGraph智能体的子组件部署
- **工具模式**：作为LangChain工具集成到现有智能体中
- **混合模式**：支持独立运行和集成运行的混合部署

### 3. 监控和日志

- **健康检查**：定期检查服务的健康状态
- **性能监控**：监控系统的性能指标
- **日志收集**：集中收集和分析日志
- **告警机制**：当出现异常时发送告警

## 安全考虑

### 1. 企业级安全框架

**SecurityManager类设计**：

```python
class SecurityManager:
    """企业级安全管理器"""

    def __init__(self):
        self.access_control = RoleBasedAccessControl()
        self.data_encryption = DataEncryptionService()
        self.audit_logger = AuditLogger()
        self.compliance_checker = ComplianceChecker()

    def validate_user_access(self, user: User, resource: Resource, operation: str) -> bool:
        """验证用户对资源的访问权限"""
        # 实现细粒度的权限控制
        role_permissions = self.access_control.get_user_permissions(user)
        resource_requirements = self.access_control.get_resource_requirements(resource)

        return self.access_control.check_permission(
            role_permissions, resource_requirements, operation
        )

    def encrypt_sensitive_data(self, data: Dict, sensitivity_level: str) -> Dict:
        """根据敏感级别加密数据"""
        if sensitivity_level in ['high', 'confidential']:
            return self.data_encryption.encrypt_with_key_rotation(data)
        elif sensitivity_level == 'medium':
            return self.data_encryption.encrypt_standard(data)
        return data

    def log_access_event(self, user: User, resource: Resource, operation: str, result: str):
        """记录访问事件用于审计"""
        self.audit_logger.log_event({
            'timestamp': datetime.now(),
            'user_id': user.id,
            'resource_id': resource.id,
            'operation': operation,
            'result': result,
            'ip_address': user.ip_address,
            'user_agent': user.user_agent
        })

class RoleBasedAccessControl:
    """基于角色的访问控制"""

    def __init__(self):
        self.roles = {
            'admin': ['read', 'write', 'delete', 'manage_users', 'manage_ontology'],
            'knowledge_engineer': ['read', 'write', 'manage_ontology'],
            'analyst': ['read', 'query', 'export'],
            'viewer': ['read', 'query']
        }
        self.resource_policies = {}

    def define_resource_policy(self, resource_type: str, policies: Dict):
        """定义资源访问策略"""
        self.resource_policies[resource_type] = policies

    def check_permission(self, user_permissions: List[str],
                        resource_requirements: List[str],
                        operation: str) -> bool:
        """检查权限"""
        required_permission = f"{operation}_{resource_requirements[0]}"
        return required_permission in user_permissions or operation in user_permissions
```

### 2. 数据安全

- **多层加密**：实现数据传输加密、存储加密和字段级加密
- **密钥管理**：使用企业级密钥管理系统，支持密钥轮换
- **数据脱敏**：对敏感数据进行自动脱敏处理
- **访问控制**：实现基于角色的细粒度访问控制
- **数据备份**：定期备份重要数据，支持增量备份和异地备份

### 3. API安全

- **认证和授权**：支持OAuth 2.0、SAML、LDAP等企业级认证
- **API网关**：统一的API入口，支持限流、熔断、监控
- **输入验证**：严格的输入验证和SQL注入防护
- **速率限制**：基于用户角色的差异化限流策略

### 4. 模型安全

- **提示词安全**：实现提示词注入检测和防护
- **输出过滤**：多层输出内容安全检查
- **模型隔离**：不同租户的模型推理隔离
- **访问审计**：完整的模型访问日志和审计轨迹

# 附：本体论概念

- 本体论(Ontology)是知识图谱的核心概念，它定义了知识图谱中实体类型和关系类型的结构化框架。简单来说，本体论就是对特定领域知识的形式化表示，它规定了我们如何对现实世界进行建模。

## 本体论的组成部分

- **实体类型(Entity Types)**: 定义知识图谱中可能出现的对象类别，如人物、地点、组织等
- **关系类型(Relationship Types)**: 定义实体之间可能存在的连接方式，如"工作于"、"位于"、"创建了"等
- **属性(Properties)**: 定义实体可能具有的特征，如人的年龄、书的出版日期等
- **规则和约束(Rules and Constraints)**: 定义实体和关系之间的逻辑规则，如"每个人只能有一个生日"

## 举个例子

假设我们要构建一个关于电影领域的知识图谱，本体论可能如下：

```python
ontology = Ontology(
    # 实体类型
    labels=[
        {"Person": "电影相关人物，如演员、导演、编剧等"},
        {"Movie": "电影作品"},
        {"Genre": "电影类型，如动作片、喜剧片等"},
        {"Studio": "电影制作公司"},
        {"Award": "电影奖项"},
    ],
    # 关系类型
    relationships=[
        "acted_in",      # 演员出演了电影
        "directed",      # 导演指导了电影
        "produced_by",   # 电影由制片公司制作
        "belongs_to",    # 电影属于某个类型
        "won",           # 电影或人物获得了奖项
    ],
)
```

使用这个本体论，我们可以从文本中提取如下知识：

- 汤姆·汉克斯(Person) --[acted_in]--> 阿甘正传(Movie)
- 史蒂文·斯皮尔伯格(Person) --[directed]--> 辛德勒的名单(Movie)
- 阿甘正传(Movie) --[belongs_to]--> 剧情片(Genre)
- 阿甘正传(Movie) --[won]--> 奥斯卡最佳影片(Award)
本体论的好处是它提供了一个结构化框架，使得LLM可以有针对性地从文本中提取实体和关系，而不是随机提取。这样构建的知识图谱会更加一致和有组织，便于后续的查询和推理。

在我们的知识图谱构建系统中，用户需要首先定义适合自己领域的本体论，然后系统会基于这个本体论从文本中提取知识，构建知识图谱。

## 开发环境设置

### 1. 环境要求

- Python 3.10+
- Neo4j 5.0+
- Docker 20.10+（可选，用于容器化部署）
- 至少8GB RAM（推荐16GB+）
- 至少20GB可用磁盘空间

### 2. 项目结构

```
knowledge-graph-builder/
├── src/                      # 源代码目录
│   ├── core/                 # 核心功能模块（框架无关）
│   │   ├── ontology/         # 本体论管理模块
│   │   ├── document/         # 文档处理模块
│   │   ├── llm/              # LLM客户端模块
│   │   ├── graph/            # 图构建和管理模块
│   │   ├── community/        # 社区检测模块
│   │   └── rag/              # GraphRAG服务模块
│   ├── langgraph/            # LangGraph集成模块
│   │   ├── agents/           # LangGraph智能体
│   │   ├── tools/            # LangChain工具
│   │   ├── workflows/        # 工作流定义
│   │   └── states/           # 状态定义
│   ├── api/                  # API服务模块
│   └── web/                  # Web界面模块
├── tests/                    # 测试目录
│   ├── unit/                 # 单元测试
│   ├── integration/          # 集成测试
│   ├── langgraph/            # LangGraph集成测试
│   └── performance/          # 性能测试
├── examples/                 # 示例代码和数据
│   ├── standalone/           # 独立使用示例
│   ├── langgraph/            # LangGraph集成示例
│   └── langchain/            # LangChain工具示例
├── docs/                     # 文档
├── scripts/                  # 实用脚本
├── config/                   # 配置文件
├── .env.example              # 环境变量示例
├── pyproject.toml            # 项目配置
├── poetry.lock               # 依赖锁定
└── README.md                 # 项目说明
```

### 3. 安装步骤

1. **克隆项目**

```bash
git clone https://github.com/yourusername/knowledge-graph-builder.git
cd knowledge-graph-builder
```

2. **使用Poetry安装依赖**

```bash
# 安装Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 配置Poetry使用项目内虚拟环境
poetry config virtualenvs.in-project true

# 安装基础依赖
poetry install

# 安装LangGraph集成依赖
poetry install --extras langgraph
```

3. **安装Neo4j**

```bash
# 使用Docker安装Neo4j
docker run \
    --name neo4j \
    -p 7474:7474 -p 7687:7687 \
    -e NEO4J_AUTH=neo4j/password \
    -e NEO4J_PLUGINS='["graph-data-science"]' \
    -v $HOME/neo4j/data:/data \
    -v $HOME/neo4j/logs:/logs \
    -v $HOME/neo4j/import:/import \
    -v $HOME/neo4j/plugins:/plugins \
    neo4j:5.9
```

4. **部署LLM服务（生产环境）**

**选项A：使用vLLM部署**

```bash
# 安装vLLM
pip install vllm

# 启动Qwen2.5模型服务
python -m vllm.entrypoints.openai.api_server \
    --model Qwen/Qwen2.5-7B-Instruct \
    --served-model-name qwen2.5-7b-instruct \
    --host 0.0.0.0 \
    --port 8000

# 或使用Docker部署
docker run --gpus all \
    -v ~/.cache/huggingface:/root/.cache/huggingface \
    -p 8000:8000 \
    vllm/vllm-openai:latest \
    --model Qwen/Qwen2.5-7B-Instruct \
    --served-model-name qwen2.5-7b-instruct
```

**选项B：使用Ollama部署**

```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 拉取并运行Qwen2.5模型
ollama pull qwen2.5:7b
ollama serve

# 测试模型
curl http://localhost:11434/api/generate \
  -d '{
    "model": "qwen2.5:7b",
    "prompt": "你好，请介绍一下自己",
    "stream": false
  }'
```

5. **配置环境变量**

```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑环境变量
nano .env
```

`.env`文件内容示例：

```
# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# LLM配置 - 生产环境（企业内部部署）
LLM_TYPE=vllm  # vllm, ollama, third_party
LLM_MODEL=qwen2.5-7b-instruct
LLM_HOST=localhost
LLM_PORT=8000

# LLM配置 - 开发调试环境（第三方API）
# LLM_TYPE=third_party
# LLM_PROVIDER=openai
# OPENAI_API_KEY=your_openai_api_key
# ZHIPU_API_KEY=your_zhipu_api_key
# DEEPSEEK_API_KEY=your_deepseek_api_key

# 应用配置
APP_ENV=production  # development, production
LOG_LEVEL=INFO

# LangGraph集成配置
ENABLE_LANGGRAPH=true  # 是否启用LangGraph集成
LANGGRAPH_CHECKPOINT_BACKEND=memory  # memory, redis, postgres
LANGGRAPH_TRACING=true  # 是否启用LangGraph追踪
```

6. **验证安装**

```bash
# 激活虚拟环境
poetry shell

# 运行测试
pytest tests/unit
```

## 详细实现指南

### 1. 本体论管理模块实现

#### 文件结构

```
src/ontology/
├── __init__.py
├── models.py       # 本体论数据模型
├── manager.py      # 本体论管理器
└── validators.py   # 本体论验证器
```

#### 核心实现

`models.py`:

```python
from typing import List, Dict, Union, Optional
from pydantic import BaseModel, Field, validator

class Ontology(BaseModel):
    """本体论模型，定义知识图谱的结构"""
    labels: List[Union[str, Dict]] = Field(
        ..., 
        description="实体类型列表，可以是字符串或带描述的字典"
    )
    relationships: List[str] = Field(
        ..., 
        description="关系类型列表"
    )
    
    @validator('labels')
    def validate_labels(cls, v):
        """验证标签格式"""
        for item in v:
            if isinstance(item, dict):
                if len(item) != 1:
                    raise ValueError("字典标签必须只包含一个键值对")
            elif not isinstance(item, str):
                raise ValueError("标签必须是字符串或字典")
        return v
    
    def get_label_names(self) -> List[str]:
        """获取所有标签名称"""
        names = []
        for item in self.labels:
            if isinstance(item, dict):
                names.extend(item.keys())
            else:
                names.append(item)
        return names
    
    def to_prompt(self) -> str:
        """将本体论转换为LLM提示"""
        prompt = "实体类型:\n"
        for item in self.labels:
            if isinstance(item, dict):
                for k, v in item.items():
                    prompt += f"- {k}: {v}\n"
            else:
                prompt += f"- {item}\n"
        
        prompt += "\n关系类型:\n"
        for rel in self.relationships:
            prompt += f"- {rel}\n"
        
        return prompt
```

`manager.py`:

```python
import json
import os
from typing import List, Dict, Union, Optional
from .models import Ontology
from .validators import validate_ontology_structure

class OntologyManager:
    """本体论管理器"""
    
    def create_ontology(self, labels: List[Union[str, Dict]], relationships: List[str]) -> Ontology:
        """创建新的本体论"""
        ontology = Ontology(labels=labels, relationships=relationships)
        validate_ontology_structure(ontology)
        return ontology
    
    def validate_ontology(self, ontology: Ontology) -> bool:
        """验证本体论的有效性"""
        try:
            validate_ontology_structure(ontology)
            return True
        except ValueError:
            return False
    
    def save_ontology(self, ontology: Ontology, path: str) -> None:
        """保存本体论到文件"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'w', encoding='utf-8') as f:
            f.write(ontology.json(indent=2))
    
    def load_ontology(self, path: str) -> Ontology:
        """从文件加载本体论"""
        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return Ontology(**data)
    
    def create_default_ontology(self) -> Ontology:
        """创建默认本体论"""
        labels = [
            {"Person": "人物名称，不包含修饰词"},
            {"Organization": "组织机构名称"},
            {"Location": "地点名称"},
            {"Event": "事件名称，不包含动词"},
            {"Concept": "概念名称"},
            {"Product": "产品名称"}
        ]
        relationships = [
            "belongs_to",
            "works_for",
            "located_in",
            "part_of",
            "created_by",
            "related_to"
        ]
        return self.create_ontology(labels, relationships)
```

### 2. 文档处理模块实现

#### 文件结构

```
src/document/
├── __init__.py
├── models.py       # 文档数据模型
├── processor.py    # 文档处理器
└── chunker.py      # 文本分块器
```

#### 核心实现

`models.py`:

```python
from typing import Dict, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime

class Document(BaseModel):
    """文档模型，包含文本内容和元数据"""
    text: str = Field(..., description="文本内容")
    metadata: Dict = Field(default_factory=dict, description="元数据")
    
    def add_metadata(self, key: str, value: any) -> None:
        """添加元数据"""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: any = None) -> any:
        """获取元数据"""
        return self.metadata.get(key, default)
    
    @classmethod
    def from_text(cls, text: str, source: str = None) -> 'Document':
        """从文本创建文档"""
        metadata = {}
        if source:
            metadata["source"] = source
        metadata["created_at"] = datetime.now().isoformat()
        return cls(text=text, metadata=metadata)
```

`processor.py`:

```python
from typing import List, Dict, Optional
from .models import Document
from .chunker import TextChunker
from ..llm.client import BaseLLMClient

class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self, llm_client: Optional[BaseLLMClient] = None):
        self.chunker = TextChunker()
        self.llm_client = llm_client
    
    def chunk_text(self, text: str, chunk_size: int = 1000) -> List[str]:
        """将文本分割成适当大小的块"""
        return self.chunker.chunk_text(text, chunk_size)
    
    def create_document(self, text: str, metadata: Dict = None) -> Document:
        """创建文档对象"""
        doc = Document(text=text, metadata=metadata or {})
        if self.llm_client and "summary" not in doc.metadata:
            summary = self.generate_summary(text)
            doc.add_metadata("summary", summary)
        return doc
    
    def process_documents(self, texts: List[str], metadata: Dict = None) -> List[Document]:
        """批量处理文本并创建文档"""
        documents = []
        base_metadata = metadata or {}
        
        for i, text in enumerate(texts):
            # 为每个文档创建独立的元数据副本
            doc_metadata = base_metadata.copy()
            doc_metadata["chunk_id"] = i
            
            doc = self.create_document(text, doc_metadata)
            documents.append(doc)
        
        return documents
    
    def generate_summary(self, text: str) -> str:
        """为文本生成摘要作为元数据"""
        if not self.llm_client:
            return ""
        
        system_prompt = (
            "请为以下文本生成一个简短的摘要，不超过50个字。"
            "只返回摘要内容，不要添加任何其他文字。"
        )
        
        try:
            summary = self.llm_client.generate(
                user_message=text,
                system_message=system_prompt
            )
            return summary.strip()
        except Exception as e:
            print(f"生成摘要时出错: {e}")
            return ""
```

### 3. LLM客户端模块实现

#### 文件结构

```
src/llm/
├── __init__.py
├── client.py       # LLM客户端基类
├── vllm.py         # vLLM部署客户端
├── ollama.py       # Ollama部署客户端
├── third_party.py  # 第三方API客户端
└── factory.py      # 客户端工厂类
```

#### 核心实现

`client.py`:

```python
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
import time
import random

class BaseLLMClient(ABC):
    """LLM客户端基类"""
    
    def __init__(self, model: str, temperature: float = 0.1, top_p: float = 0.5):
        self.model = model
        self.temperature = temperature
        self.top_p = top_p
    
    @abstractmethod
    def generate(self, user_message: str, system_message: Optional[str] = None) -> str:
        """调用LLM生成文本"""
        pass
    
    def _exponential_backoff(self, retry: int, max_retries: int = 5, base_delay: float = 1.0) -> float:
        """指数退避策略"""
        if retry >= max_retries:
            raise Exception(f"达到最大重试次数 {max_retries}")
        
        # 计算延迟时间，添加随机抖动
        delay = base_delay * (2 ** retry) + random.uniform(0, 0.5)
        time.sleep(delay)
        return delay
```

`vllm.py`:

```python
import os
from typing import Optional, Dict, Any, List
import openai
from .client import BaseLLMClient

class VLLMClient(BaseLLMClient):
    """vLLM部署的模型客户端"""
    
    def __init__(self, 
                 model: str = "qwen2.5-7b-instruct", 
                 host: str = "localhost", 
                 port: int = 8000,
                 temperature: float = 0.1, 
                 top_p: float = 0.5):
        super().__init__(model, temperature, top_p)
        self.client = openai.OpenAI(
            base_url=f"http://{host}:{port}/v1",
            api_key="EMPTY"  # vLLM不需要API密钥
        )
    
    def generate(self, user_message: str, system_message: Optional[str] = None) -> str:
        """调用vLLM部署的模型生成文本"""
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        messages.append({"role": "user", "content": user_message})
        
        retry = 0
        max_retries = 5
        
        while True:
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    top_p=self.top_p,
                    max_tokens=2048
                )
                return response.choices[0].message.content
            except Exception as e:
                retry += 1
                self._exponential_backoff(retry, max_retries)
                print(f"vLLM API错误，正在重试 ({retry}/{max_retries}): {e}")

`ollama.py`:

```python
import requests
import json
from typing import Optional
from .client import BaseLLMClient

class OllamaClient(BaseLLMClient):
    """Ollama部署的模型客户端"""
    
    def __init__(self, 
                 model: str = "qwen2.5:7b", 
                 host: str = "localhost", 
                 port: int = 11434,
                 temperature: float = 0.1, 
                 top_p: float = 0.5):
        super().__init__(model, temperature, top_p)
        self.base_url = f"http://{host}:{port}"
    
    def generate(self, user_message: str, system_message: Optional[str] = None) -> str:
        """调用Ollama部署的模型生成文本"""
        prompt = user_message
        if system_message:
            prompt = f"System: {system_message}\n\nUser: {user_message}"
        
        retry = 0
        max_retries = 5
        
        while True:
            try:
                response = requests.post(
                    f"{self.base_url}/api/generate",
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False,
                        "options": {
                            "temperature": self.temperature,
                            "top_p": self.top_p
                        }
                    },
                    timeout=60
                )
                response.raise_for_status()
                return response.json()["response"]
            except Exception as e:
                retry += 1
                self._exponential_backoff(retry, max_retries)
                print(f"Ollama API错误，正在重试 ({retry}/{max_retries}): {e}")

`third_party.py`:

```python
import os
from typing import Optional, Dict, Any
import openai
from .client import BaseLLMClient

class ThirdPartyClient(BaseLLMClient):
    """第三方API服务客户端（用于开发调试）"""
    
    PROVIDER_CONFIGS = {
        "openai": {
            "base_url": "https://api.openai.com/v1",
            "default_model": "gpt-3.5-turbo",
            "api_key_env": "OPENAI_API_KEY"
        },
        "azure": {
            "base_url": "https://your-resource.openai.azure.com/",
            "default_model": "gpt-35-turbo",
            "api_key_env": "AZURE_OPENAI_API_KEY"
        },
        "zhipu": {
            "base_url": "https://open.bigmodel.cn/api/paas/v4/",
            "default_model": "glm-4",
            "api_key_env": "ZHIPU_API_KEY"
        },
        "deepseek": {
            "base_url": "https://api.deepseek.com/v1",
            "default_model": "deepseek-chat",
            "api_key_env": "DEEPSEEK_API_KEY"
        }
    }
    
    def __init__(self, 
                 provider: str = "openai", 
                 model: str = None,
                 api_key: str = None,
                 base_url: str = None,
                 temperature: float = 0.1, 
                 top_p: float = 0.5):
        
        config = self.PROVIDER_CONFIGS.get(provider, self.PROVIDER_CONFIGS["openai"])
        
        self.model = model or config["default_model"]
        self.api_key = api_key or os.environ.get(config["api_key_env"])
        self.base_url = base_url or config["base_url"]
        
        super().__init__(self.model, temperature, top_p)
        
        self.client = openai.OpenAI(
            base_url=self.base_url,
            api_key=self.api_key
        )
    
    def generate(self, user_message: str, system_message: Optional[str] = None) -> str:
        """调用第三方API生成文本"""
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        messages.append({"role": "user", "content": user_message})
        
        retry = 0
        max_retries = 5
        
        while True:
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    top_p=self.top_p
                )
                return response.choices[0].message.content
            except Exception as e:
                retry += 1
                self._exponential_backoff(retry, max_retries)
                print(f"第三方API错误，正在重试 ({retry}/{max_retries}): {e}")

`factory.py`:

```python
from typing import Dict, Any, Optional
from .vllm import VLLMClient
from .ollama import OllamaClient
from .third_party import ThirdPartyClient
from .client import BaseLLMClient

class LLMClientFactory:
    """LLM客户端工厂类"""
    
    @staticmethod
    def create_client(client_type: str, **kwargs) -> BaseLLMClient:
        """创建LLM客户端"""
        if client_type == "vllm":
            return VLLMClient(**kwargs)
        elif client_type == "ollama":
            return OllamaClient(**kwargs)
        elif client_type == "third_party":
            return ThirdPartyClient(**kwargs)
        else:
            raise ValueError(f"不支持的客户端类型: {client_type}")
    
    @staticmethod
    def create_from_config(config: Dict[str, Any]) -> BaseLLMClient:
        """从配置创建LLM客户端"""
        client_type = config.pop("type")
        return LLMClientFactory.create_client(client_type, **config)
```

### 4. 图构建器模块实现

#### 文件结构

```
src/graph/
├── __init__.py
├── models.py       # 图数据模型
├── maker.py        # 图构建器
└── neo4j.py        # Neo4j图模型
```

#### 核心实现

`models.py`:

```python
from typing import Dict, Optional, List, Union
from pydantic import BaseModel, Field

class Node(BaseModel):
    """知识图谱中的节点，表示实体"""
    label: str = Field(..., description="实体类型")
    name: str = Field(..., description="实体名称")
    
    def __eq__(self, other):
        if not isinstance(other, Node):
            return False
        return self.label == other.label and self.name == other.name
    
    def __hash__(self):
        return hash((self.label, self.name))

class Edge(BaseModel):
    """知识图谱中的边，表示关系"""
    node_1: Node = Field(..., description="起始节点")
    node_2: Node = Field(..., description="终止节点")
    relationship: str = Field(..., description="关系类型")
    metadata: Dict = Field(default_factory=dict, description="元数据")
    order: Optional[int] = Field(None, description="顺序（可选）")
    
    def __eq__(self, other):
        if not isinstance(other, Edge):
            return False
        return (
            self.node_1 == other.node_1 and
            self.node_2 == other.node_2 and
            self.relationship == other.relationship
        )
    
    def __hash__(self):
        return hash((hash(self.node_1), hash(self.node_2), self.relationship))
```

`maker.py`:

```python
import json
import time
from typing import List, Dict, Optional
from ..ontology.models import Ontology
from ..document.models import Document
from ..llm.client import BaseLLMClient
from .models import Node, Edge

class GraphMaker:
    """图构建器"""
    
    def __init__(self, ontology: Ontology, llm_client: BaseLLMClient, verbose: bool = False):
        self.ontology = ontology
        self.llm_client = llm_client
        self.verbose = verbose
    
    def from_document(self, document: Document) -> List[Edge]:
        """从单个文档中提取知识图谱"""
        if self.verbose:
            print(f"处理文档: {document.get_metadata('source', 'unknown')}")
        
        edges = self._extract_entities_and_relations(document.text)
        
        # 将文档元数据添加到每个边
        for edge in edges:
            edge.metadata.update(document.metadata)
        
        return edges
    
    def from_documents(self, documents: List[Document], delay_s_between: int = 0) -> List[Edge]:
        """从多个文档中提取知识图谱"""
        all_edges = []
        
        for i, doc in enumerate(documents):
            if self.verbose:
                print(f"处理文档 {i+1}/{len(documents)}")
            
            edges = self.from_document(doc)
            all_edges.extend(edges)
            
            if delay_s_between > 0 and i < len(documents) - 1:
                time.sleep(delay_s_between)
        
        # 去除重复边
        unique_edges = list({(edge.node_1, edge.node_2, edge.relationship): edge for edge in all_edges}.values())
        
        if self.verbose:
            print(f"总共提取了 {len(unique_edges)} 条唯一边")
        
        return unique_edges
    
    def _extract_entities_and_relations(self, text: str) -> List[Edge]:
        """使用LLM从文本中提取实体和关系"""
        system_prompt = self._create_extraction_prompt()
        
        try:
            response = self.llm_client.generate(
                user_message=text,
                system_message=system_prompt
            )
            
            return self._parse_llm_response(response)
        except Exception as e:
            print(f"提取实体和关系时出错: {e}")
            return []
    
    def _create_extraction_prompt(self) -> str:
        """创建实体关系提取提示"""
        prompt = (
            "你是一个专业的知识图谱构建助手。你的任务是从给定文本中提取实体和关系，"
            "并按照指定格式输出。\n\n"
            "请按照以下本体论提取实体和关系：\n\n"
        )
        
        prompt += self.ontology.to_prompt()
        
        prompt += (
            "\n输出格式要求：\n"
            "请使用JSON格式输出，包含一个edges数组，每个边包含以下字段：\n"
            "- node_1: 包含label和name的对象\n"
            "- node_2: 包含label和name的对象\n"
            "- relationship: 关系类型\n\n"
            "示例输出：\n"
            "```json\n"
            "{\n"
            '  "edges": [\n'
            '    {\n'
            '      "node_1": {"label": "Person", "name": "张三"},\n'
            '      "node_2": {"label": "Organization", "name": "ABC公司"},\n'
            '      "relationship": "works_for"\n'
            '    }\n'
            "  ]\n"
            "}\n"
            "```\n\n"
            "请只输出JSON格式的结果，不要添加任何其他文字。"
        )
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> List[Edge]:
        """解析LLM响应，提取边列表"""
        try:
            # 尝试直接解析JSON
            data = json.loads(response)
            
            if "edges" not in data:
                print("响应中缺少edges字段")
                return []
            
            edges = []
            for edge_data in data["edges"]:
                try:
                    edge = Edge(
                        node_1=Node(**edge_data["node_1"]),
                        node_2=Node(**edge_data["node_2"]),
                        relationship=edge_data["relationship"]
                    )
                    edges.append(edge)
                except Exception as e:
                    print(f"解析边时出错: {e}")
            
            return edges
        except json.JSONDecodeError:
            print("无法解析LLM响应为JSON")
            
            # 尝试从文本中提取JSON部分
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                try:
                    data = json.loads(json_match.group(1))
                    if "edges" in data:
                        return [Edge(**edge) for edge in data["edges"]]
                except:
                    pass
            
            return []
```

## 开发最佳实践

### 1. 代码风格和质量

- 使用[Black](https://github.com/psf/black)格式化代码
- 使用[isort](https://github.com/PyCQA/isort)排序导入
- 使用[flake8](https://github.com/PyCQA/flake8)检查代码质量
- 使用[mypy](https://github.com/python/mypy)进行类型检查

配置示例（`pyproject.toml`）：

```toml
[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
```

### 2. 测试策略

- 为每个模块编写单元测试
- 使用pytest作为测试框架
- 使用pytest-cov检查测试覆盖率
- 使用mock模拟外部依赖

测试示例：

```python
# tests/unit/ontology/test_manager.py
import pytest
from src.ontology.manager import OntologyManager
from src.ontology.models import Ontology

def test_create_ontology():
    manager = OntologyManager()
    labels = ["Person", "Organization"]
    relationships = ["works_for"]
    
    ontology = manager.create_ontology(labels, relationships)
    
    assert isinstance(ontology, Ontology)
    assert ontology.labels == labels
    assert ontology.relationships == relationships

def test_validate_ontology():
    manager = OntologyManager()
    valid_ontology = Ontology(labels=["Person"], relationships=["works_for"])
    
    assert manager.validate_ontology(valid_ontology) is True
```

### 3. 文档生成

使用[Sphinx](https://www.sphinx-doc.org/)生成API文档：

```bash
# 安装Sphinx
poetry add --dev sphinx sphinx-rtd-theme

# 初始化文档
sphinx-quickstart docs

# 生成API文档
sphinx-apidoc -o docs/api src
```

### 4. 持续集成

使用GitHub Actions进行持续集成：

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
    - name: Install dependencies
      run: |
        poetry install
    - name: Lint
      run: |
        poetry run black --check src tests
        poetry run isort --check src tests
        poetry run flake8 src tests
    - name: Type check
      run: |
        poetry run mypy src
    - name: Test
      run: |
        poetry run pytest --cov=src tests/
```

## 部署指南

### 1. Docker部署

`Dockerfile`:

```dockerfile
FROM python:3.10-slim

WORKDIR /app

# 安装Poetry
RUN pip install poetry

# 复制项目文件
COPY pyproject.toml poetry.lock ./
COPY src ./src

# 配置Poetry不创建虚拟环境
RUN poetry config virtualenvs.create false

# 安装依赖
RUN poetry install --no-dev

# 设置环境变量
ENV PYTHONPATH=/app

# 运行应用
CMD ["python", "-m", "src.api.main"]
```

`docker-compose.yml`:

```yaml
version: '3'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - neo4j
  
  neo4j:
    image: neo4j:5.9
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_PLUGINS=["graph-data-science"]
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/import
      - neo4j_plugins:/plugins

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
```

### 2. Kubernetes部署

`kubernetes/deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledge-graph-builder
spec:
  replicas: 2
  selector:
    matchLabels:
      app: knowledge-graph-builder
  template:
    metadata:
      labels:
        app: knowledge-graph-builder
    spec:
      containers:
      - name: knowledge-graph-builder
        image: your-registry/knowledge-graph-builder:latest
        ports:
        - containerPort: 8000
        env:
        - name: NEO4J_URI
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: neo4j_uri
        - name: NEO4J_USER
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: neo4j_user
        - name: NEO4J_PASSWORD
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: neo4j_password
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: openai_api_key
```

## 性能优化指南

### 1. LLM调用优化

- **批处理**：将多个小文档合并为一个批次，减少API调用次数
- **缓存**：缓存常见查询的LLM响应
- **并行处理**：使用异步IO并行处理多个文档

示例代码：

```python
import asyncio
from typing import List, Dict
from ..document.models import Document
from ..llm.client import BaseLLMClient

async def process_document_batch(documents: List[Document], llm_client: BaseLLMClient) -> List[Dict]:
    """并行处理多个文档"""
    async def process_one(doc: Document):
        # 处理单个文档的逻辑
        return {"id": doc.get_metadata("id"), "result": "processed"}
    
    tasks = [process_one(doc) for doc in documents]
    return await asyncio.gather(*tasks)
```

### 2. 图数据库优化

- **索引优化**：为常用查询创建索引
- **查询优化**：优化Cypher查询，避免全图扫描
- **批量操作**：使用批量操作而不是单条操作

Neo4j索引示例：

```cypher
// 为节点名称创建索引
CREATE INDEX node_name_index FOR (n:Node) ON (n.name);

// 为关系类型创建索引
CREATE INDEX relationship_type_index FOR ()-[r]-() ON (r.type);
```

### 3. 内存优化

- **流式处理**：使用生成器和迭代器处理大型数据集
- **分批加载**：分批加载大型图数据，避免内存溢出

示例代码：

```python
def process_large_dataset(file_path: str, batch_size: int = 1000):
    """分批处理大型数据集"""
    def read_batches():
        with open(file_path, 'r') as f:
            batch = []
            for line in f:
                batch.append(line.strip())
                if len(batch) >= batch_size:
                    yield batch
                    batch = []
            if batch:  # 处理最后一个不完整的批次
                yield batch
    
    for batch in read_batches():
        process_batch(batch)
```

## 故障排除指南

### 1. 常见问题及解决方案

#### LLM API错误

**问题**：调用LLM API时出现速率限制错误
**解决方案**：

- 实现指数退避重试
- 减少并发请求数
- 使用多个API密钥轮换

#### Neo4j连接问题

**问题**：无法连接到Neo4j数据库
**解决方案**：

- 检查Neo4j服务是否运行
- 验证连接URI、用户名和密码
- 检查网络连接和防火墙设置

#### 内存溢出

**问题**：处理大型图数据时出现内存溢出
**解决方案**：

- 分批处理数据
- 增加应用的内存限制
- 优化数据结构，减少内存使用

### 2. 日志和监控

- 使用结构化日志记录关键操作
- 监控系统资源使用情况
- 跟踪LLM API调用统计

日志配置示例：

```python
import logging
import structlog

def configure_logging():
    """配置结构化日志"""
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    logging.basicConfig(
        format="%(message)s",
        level=logging.INFO,
    )

# 使用示例
logger = structlog.get_logger()
logger.info("处理文档", document_id="doc123", status="success")
```

## 扩展和集成指南

### 1. 支持新的LLM提供商

要添加新的LLM提供商支持，需要：

1. 创建新的客户端类，继承`BaseLLMClient`
2. 实现`generate`方法
3. 注册客户端到工厂类

示例：

```python
# src/llm/anthropic.py
from typing import Optional
import anthropic
from .client import BaseLLMClient

class AnthropicClient(BaseLLMClient):
    """Anthropic Claude模型客户端"""
    
    def __init__(self, model: str = "claude-3-opus-20240229", temperature: float = 0.1, top_p: float = 0.5):
        super().__init__(model, temperature, top_p)
        self.client = anthropic.Anthropic()
    
    def generate(self, user_message: str, system_message: Optional[str] = None) -> str:
        """调用Anthropic模型生成文本"""
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        
        messages.append({"role": "user", "content": user_message})
        
        response = self.client.messages.create(
            model=self.model,
            messages=messages,
            temperature=self.temperature,
            top_p=self.top_p
        )
        
        return response.content[0].text
```

### 2. 与其他系统集成

#### 与向量数据库集成

```python
# src/vector/chroma_client.py
import chromadb
from chromadb.utils import embedding_functions
import numpy as np
from typing import List, Dict, Any

class ChromaClient:
    """Chroma向量数据库客户端"""
    
    def __init__(self, collection_name: str = "documents"):
        self.client = chromadb.Client()
        self.embedding_function = embedding_functions.OpenAIEmbeddingFunction(
            api_key=os.environ.get("OPENAI_API_KEY"),
            model_name="text-embedding-ada-002"
        )
        self.collection = self.client.get_or_create_collection(
            name=collection_name,
            embedding_function=self.embedding_function
        )
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> None:
        """添加文档到向量数据库"""
        ids = [doc["id"] for doc in documents]
        texts = [doc["text"] for doc in documents]
        metadatas = [doc.get("metadata", {}) for doc in documents]
        
        self.collection.add(
            ids=ids,
            documents=texts,
            metadatas=metadatas
        )
    
    def search(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        results = self.collection.query(
            query_texts=[query],
            n_results=n_results
        )
        
        return [
            {
                "id": results["ids"][0][i],
                "text": results["documents"][0][i],
                "metadata": results["metadatas"][0][i],
                "distance": results["distances"][0][i]
            }
            for i in range(len(results["ids"][0]))
        ]
```

#### 与Web应用集成

```python
# src/api/main.py
from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Dict, Any
from ..graph.neo4j import Neo4jGraphModel
from ..rag.service import GraphRAGService

app = FastAPI(title="知识图谱构建系统API")

class QueryRequest(BaseModel):
    query: str
    mode: str = "hybrid"  # local, global, hybrid

class QueryResponse(BaseModel):
    answer: str
    sources: List[Dict[str, Any]]

@app.post("/api/query", response_model=QueryResponse)
async def query(request: QueryRequest):
    """查询知识图谱"""
    try:
        graph_model = get_graph_model()
        rag_service = get_rag_service(graph_model)
        
        if request.mode == "local":
            answer = rag_service.local_graph_rag(request.query)
        elif request.mode == "global":
            answer = rag_service.global_graph_rag(request.query)
        else:  # hybrid
            answer = rag_service.hybrid_graph_rag(request.query)
        
        # 获取源文档信息
        sources = rag_service.get_sources()
        
        return QueryResponse(answer=answer, sources=sources)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## 未来扩展路线图

1. **多模态支持**：扩展系统以处理图像、音频和视频数据
2. **实时更新**：支持知识图谱的实时更新和增量构建
3. **多语言支持**：扩展系统以支持多种语言的文本处理
4. **用户反馈集成**：整合用户反馈机制，改进知识图谱质量
5. **自动化本体论学习**：从数据中自动学习和优化本体论
6. **分布式处理**：支持大规模分布式知识图谱构建
7. **知识推理**：增强系统的推理能力，支持复杂查询和推理
8. **时序知识图谱**：支持时间维度，跟踪知识的演变
