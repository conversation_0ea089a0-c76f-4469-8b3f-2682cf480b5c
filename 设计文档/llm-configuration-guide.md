# LLM配置指南

## 🎯 概述

知识图谱MVP系统支持多种LLM提供商，包括智谱AI、ModelScope、Ollama和OpenRouter。您可以根据需求选择最适合的LLM服务。

## 🔧 支持的LLM提供商

### 1. ModelScope（推荐，默认）
- **优势**: 免费使用、响应速度快、中文支持好
- **模型**: Qwen/Qwen3-235B-A22B
- **适用场景**: 开发测试、中文文本处理

### 2. 智谱AI（GLM-4-Flash）
- **优势**: 专业的中文大模型、理解能力强
- **模型**: GLM-4-Flash
- **适用场景**: 生产环境、高质量文本处理

### 3. Ollama（本地部署）
- **优势**: 完全本地化、数据隐私保护
- **模型**: qwen3:1.7b
- **适用场景**: 离线环境、敏感数据处理

### 4. OpenRouter
- **优势**: 多模型选择、国际化支持
- **模型**: qwen/qwen3-30b-a3b:free
- **适用场景**: 多语言处理、模型对比测试

## 📝 配置步骤

### 步骤1：获取API密钥

#### ModelScope
1. 访问 [ModelScope](https://www.modelscope.cn/)
2. 注册并登录账户
3. 进入个人中心 → API Token
4. 创建新的API Token

#### 智谱AI
1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册并登录账户
3. 进入控制台 → API密钥管理
4. 创建新的API密钥

#### OpenRouter
1. 访问 [OpenRouter](https://openrouter.ai/)
2. 注册并登录账户
3. 进入Settings → Keys
4. 创建新的API密钥

#### Ollama（本地）
1. 下载并安装 [Ollama](https://ollama.ai/download)
2. 运行命令下载模型：
   ```bash
   ollama pull qwen3:1.7b
   ```

### 步骤2：配置API密钥

#### 方法1：修改llm_config.json（推荐）
```json
{
  "models": {
    "zhipu": {
      "provider": "zhipu",
      "api_key": "你的智谱AI密钥",
      "base_url": "https://open.bigmodel.cn/api/paas/v4/",
      "model_name": "GLM-4-Flash",
      "description": "智谱 GLM-4-Flash 模型"
    },
    "modelscope": {
      "provider": "modelscope",
      "api_key": "你的ModelScope密钥",
      "base_url": "https://api-inference.modelscope.cn/v1/",
      "model_name": "Qwen/Qwen3-235B-A22B",
      "description": "ModelScope Qwen 模型"
    },
    "ollama": {
      "provider": "ollama",
      "api_key": "ollama",
      "base_url": "http://localhost:11434/v1",
      "model_name": "qwen3:1.7b",
      "description": "本地 Ollama 模型"
    },
    "openrouter": {
      "provider": "openrouter",
      "api_key": "你的OpenRouter密钥",
      "base_url": "https://openrouter.ai/api/v1",
      "model_name": "qwen/qwen3-30b-a3b:free",
      "description": "OpenRouter qwen3 30B 模型"
    }
  },
  "default_provider": "modelscope"
}
```

#### 方法2：修改.env文件
```env
# 智谱AI配置
ZHIPU_API_KEY=你的智谱AI密钥

# ModelScope配置
MODELSCOPE_API_KEY=你的ModelScope密钥

# OpenRouter配置
OPENROUTER_API_KEY=你的OpenRouter密钥
```

### 步骤3：选择默认提供商

在`llm_config.json`中修改`default_provider`字段：
```json
{
  "default_provider": "modelscope"  // 可选: zhipu, modelscope, ollama, openrouter
}
```

## 🚀 使用方法

### 在Web界面中切换
1. 启动系统后，打开前端界面
2. 在左侧边栏找到"LLM设置"
3. 从下拉菜单中选择LLM提供商
4. 系统会自动使用选择的提供商进行处理

### 通过API调用
```python
# 提取知识图谱时指定提供商
response = requests.post(
    "http://localhost:8000/api/documents/extract",
    json={
        "text": "你的文本内容",
        "provider": "zhipu"  # 指定使用智谱AI
    }
)

# 智能查询时指定提供商
response = requests.post(
    "http://localhost:8000/api/graph/query",
    json={
        "question": "你的问题",
        "provider": "modelscope"  # 指定使用ModelScope
    }
)
```

## 🔍 性能对比

| 提供商 | 响应速度 | 中文理解 | 成本 | 稳定性 | 推荐指数 |
|--------|----------|----------|------|--------|----------|
| ModelScope | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 免费 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 智谱AI | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 付费 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Ollama | ⭐⭐⭐ | ⭐⭐⭐⭐ | 免费 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| OpenRouter | ⭐⭐⭐ | ⭐⭐⭐ | 部分免费 | ⭐⭐⭐ | ⭐⭐⭐ |

## 🐛 故障排除

### 常见问题

#### 1. API密钥无效
```
错误: 401 Unauthorized
解决: 检查API密钥是否正确，是否已过期
```

#### 2. 网络连接失败
```
错误: Connection timeout
解决: 检查网络连接，尝试使用VPN
```

#### 3. 模型不存在
```
错误: Model not found
解决: 检查模型名称是否正确，是否支持该模型
```

#### 4. Ollama连接失败
```
错误: Connection refused
解决: 
1. 确认Ollama服务正在运行: ollama serve
2. 检查模型是否已下载: ollama list
3. 重新下载模型: ollama pull qwen3:1.7b
```

### 调试技巧

#### 1. 查看详细错误信息
在终端中查看API服务的日志输出，了解具体错误原因。

#### 2. 测试API连接
```bash
# 测试ModelScope API
curl -X POST "https://api-inference.modelscope.cn/v1/chat/completions" \
  -H "Authorization: Bearer 你的密钥" \
  -H "Content-Type: application/json" \
  -d '{"model": "Qwen/Qwen3-235B-A22B", "messages": [{"role": "user", "content": "Hello"}]}'

# 测试智谱AI API
curl -X POST "https://open.bigmodel.cn/api/paas/v4/chat/completions" \
  -H "Authorization: Bearer 你的密钥" \
  -H "Content-Type: application/json" \
  -d '{"model": "GLM-4-Flash", "messages": [{"role": "user", "content": "Hello"}]}'
```

#### 3. 检查配置文件
确保`llm_config.json`格式正确，API密钥已正确填入。

## 💡 最佳实践

### 1. 提供商选择建议
- **开发测试**: 使用ModelScope（免费、快速）
- **生产环境**: 使用智谱AI（质量高、稳定）
- **离线环境**: 使用Ollama（本地部署）
- **多语言**: 使用OpenRouter（模型选择多）

### 2. 成本优化
- 优先使用免费的ModelScope进行开发测试
- 生产环境根据实际需求选择付费服务
- 对于敏感数据，建议使用本地Ollama

### 3. 性能优化
- 根据文本长度选择合适的模型
- 使用缓存避免重复调用
- 设置合理的超时时间

### 4. 安全考虑
- 不要在代码中硬编码API密钥
- 使用环境变量或配置文件管理密钥
- 定期轮换API密钥

## 🔄 切换提供商

系统支持动态切换LLM提供商，无需重启服务：

1. **前端切换**: 在Web界面侧边栏选择不同的提供商
2. **配置切换**: 修改`llm_config.json`中的`default_provider`
3. **API切换**: 在API调用中指定`provider`参数

切换后，系统会自动使用新的提供商处理后续请求。

## 📞 获取支持

如果遇到配置问题：
1. 检查API密钥是否有效
2. 确认网络连接正常
3. 查看系统日志获取详细错误信息
4. 参考各提供商的官方文档

通过合理配置LLM提供商，您可以获得最佳的知识图谱构建体验！
