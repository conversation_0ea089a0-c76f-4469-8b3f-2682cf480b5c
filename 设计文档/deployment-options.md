# 部署选择指南

## 🎯 概述

知识图谱MVP系统提供了灵活的部署选择，您可以根据自己的环境和需求选择最适合的部署方式。

## 🗄️ Neo4j数据库部署选择

### 方式1：本地安装Neo4j（推荐开发环境）

#### 优势
- ✅ 无需Docker环境
- ✅ 性能更好，直接访问
- ✅ 便于调试和开发
- ✅ 资源占用更少
- ✅ 启动速度快

#### 适用场景
- 个人开发学习
- 原型验证
- 不熟悉Docker的开发者
- 资源受限的环境

#### 安装步骤
```bash
# 1. 下载Neo4j Desktop
# 访问：https://neo4j.com/download/

# 2. 或命令行安装
# macOS: brew install neo4j
# Ubuntu: sudo apt install neo4j=1:5.13.0
# Windows: 下载安装包

# 3. 启动服务
neo4j start
```

#### 配置要求
- Java 17+
- 2GB+ 可用内存
- 1GB+ 磁盘空间

### 方式2：Docker部署（推荐生产环境）

#### 优势
- ✅ 环境隔离
- ✅ 版本一致性
- ✅ 便于部署和迁移
- ✅ 支持容器编排

#### 适用场景
- 生产环境部署
- 团队协作开发
- CI/CD流水线
- 云环境部署

#### 部署步骤
```bash
# 1. 确保Docker已安装
docker --version

# 2. 启动Neo4j容器
docker-compose up -d neo4j

# 3. 验证服务
curl http://localhost:7474
```

#### 配置要求
- Docker Desktop
- 4GB+ 可用内存
- 2GB+ 磁盘空间

## 🤖 LLM服务部署选择

### 方式1：在线LLM服务（推荐）

#### 优势
- ✅ 无需本地资源
- ✅ 模型质量高
- ✅ 多种选择
- ✅ 快速启动

#### 支持的服务商
1. **ModelScope**（免费，推荐）
   - 模型：Qwen/Qwen3-235B-A22B
   - 注册：https://www.modelscope.cn/

2. **智谱AI**（付费，质量高）
   - 模型：GLM-4-Flash
   - 注册：https://open.bigmodel.cn/

3. **OpenRouter**（部分免费）
   - 模型：qwen/qwen3-30b-a3b:free
   - 注册：https://openrouter.ai/

#### 配置步骤
```bash
# 1. 获取API密钥
# 2. 配置llm_config.json
# 3. 在前端选择LLM提供商
```

### 方式2：本地Ollama部署

#### 优势
- ✅ 数据隐私保护
- ✅ 离线可用
- ✅ 无API调用费用
- ✅ 完全可控

#### 适用场景
- 敏感数据处理
- 离线环境
- 长期大量使用
- 对隐私要求高

#### 部署步骤
```bash
# 1. 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. 下载模型
ollama pull qwen3:1.7b

# 3. 启动服务
ollama serve
```

#### 资源要求
- 8GB+ 内存
- 4GB+ 磁盘空间
- 支持的CPU/GPU

## 📊 部署方案对比

| 方案 | Neo4j | LLM | 内存需求 | 磁盘需求 | 网络需求 | 适用场景 |
|------|-------|-----|----------|----------|----------|----------|
| 轻量级 | 本地安装 | 在线服务 | 2-4GB | 1-2GB | 稳定网络 | 个人开发、学习 |
| 标准型 | Docker | 在线服务 | 4-6GB | 2-3GB | 稳定网络 | 团队开发、演示 |
| 完全本地 | 本地安装 | Ollama | 8-12GB | 5-8GB | 无需网络 | 离线环境、隐私保护 |
| 生产型 | Docker | 在线服务 | 6-8GB | 3-5GB | 高速网络 | 生产部署、高可用 |

## 🚀 推荐部署方案

### 新手开发者
```bash
# 推荐：本地Neo4j + 在线LLM
1. 下载Neo4j Desktop
2. 创建数据库（版本5.13.0，密码password123）
3. 注册ModelScope获取API密钥
4. 运行 python mvp-project-setup.py
5. 执行 ./start.sh
```

### 有经验开发者
```bash
# 推荐：Docker + 在线LLM
1. 安装Docker Desktop
2. 配置多个LLM服务商API密钥
3. 运行 python mvp-project-setup.py
4. 执行 docker-compose up -d neo4j
5. 执行 ./start.sh
```

### 企业用户
```bash
# 推荐：Docker + 本地Ollama
1. 部署Docker环境
2. 安装Ollama服务
3. 配置企业级安全设置
4. 部署到生产环境
```

## 🔧 环境检查清单

### 开发环境准备
- [ ] Python 3.8+ 已安装
- [ ] 网络连接正常
- [ ] 至少4GB可用内存
- [ ] 至少2GB可用磁盘空间

### Neo4j环境检查
- [ ] Neo4j服务正在运行
- [ ] 端口7687和7474可访问
- [ ] 用户名密码配置正确
- [ ] 数据库版本为5.13.0

### LLM服务检查
- [ ] API密钥已获取并配置
- [ ] 网络可访问LLM服务
- [ ] 或Ollama服务正在运行

## 🐛 常见问题解决

### Neo4j连接问题
```bash
# 检查服务状态
neo4j status

# 检查端口占用
netstat -tulpn | grep 7687

# 重启服务
neo4j restart
```

### Docker问题
```bash
# 检查Docker状态
docker --version
docker ps

# 重启容器
docker-compose restart neo4j
```

### LLM服务问题
```bash
# 检查API密钥
curl -H "Authorization: Bearer YOUR_API_KEY" API_ENDPOINT

# 检查Ollama服务
ollama list
curl http://localhost:11434/api/tags
```

## 📈 性能优化建议

### 本地Neo4j优化
```conf
# neo4j.conf
dbms.memory.heap.max_size=2G
dbms.memory.pagecache.size=1G
```

### Docker优化
```yaml
# docker-compose.yml
services:
  neo4j:
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

### LLM服务优化
- 选择地理位置最近的服务商
- 配置合理的超时时间
- 使用缓存减少重复调用

## 🔄 迁移指南

### 从Docker迁移到本地Neo4j
```bash
# 1. 导出Docker数据
docker exec neo4j-container neo4j-admin database dump neo4j

# 2. 停止Docker容器
docker-compose down

# 3. 安装本地Neo4j
# 4. 导入数据
neo4j-admin database load neo4j --from-path=./backup
```

### 从本地迁移到Docker
```bash
# 1. 备份本地数据
neo4j-admin database dump neo4j

# 2. 停止本地服务
neo4j stop

# 3. 启动Docker容器
docker-compose up -d neo4j

# 4. 导入数据到容器
docker exec -it neo4j-container neo4j-admin database load neo4j
```

## 💡 最佳实践

### 开发环境
- 使用本地Neo4j，便于调试
- 配置多个LLM服务商，避免单点故障
- 定期备份数据库

### 生产环境
- 使用Docker部署，便于管理
- 配置监控和日志
- 设置数据备份策略
- 使用负载均衡

### 安全考虑
- 不要在代码中硬编码API密钥
- 使用HTTPS连接
- 定期更新依赖包
- 配置防火墙规则

通过合理选择部署方案，您可以获得最适合自己需求的知识图谱MVP系统！
