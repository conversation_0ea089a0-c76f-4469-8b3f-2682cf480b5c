#!/usr/bin/env python3
"""
知识图谱MVP项目自动创建脚本
运行此脚本将自动创建完整的项目结构和所有必要文件
"""

import os
import sys
from pathlib import Path

def create_directory_structure():
    """创建项目目录结构"""
    directories = [
        "knowledge-graph-mvp",
        "knowledge-graph-mvp/src",
        "knowledge-graph-mvp/src/models",
        "knowledge-graph-mvp/src/services", 
        "knowledge-graph-mvp/src/api",
        "knowledge-graph-mvp/src/utils",
        "knowledge-graph-mvp/frontend",
        "knowledge-graph-mvp/frontend/pages",
        "knowledge-graph-mvp/frontend/components",
        "knowledge-graph-mvp/tests",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")

def create_pyproject_toml():
    """创建pyproject.toml文件"""
    content = """[project]
name = "knowledge-graph-mvp"
version = "0.1.0"
description = "知识图谱构建系统MVP"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "streamlit>=1.28.1",
    "neo4j>=5.14.1",
    "requests>=2.31.0",
    "pyvis>=0.3.2",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.0",
]
requires-python = ">=3.8"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

# 配置镜像源（阿里云主源，清华备用源）
[[tool.uv.index]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"
"""
    with open("knowledge-graph-mvp/pyproject.toml", "w") as f:
        f.write(content)
    print("✅ 创建文件: pyproject.toml")

def create_docker_compose():
    """创建docker-compose.yml文件（可选）"""
    content = """# Docker方式部署Neo4j（可选）
# 如果你不想使用Docker，请直接安装本地Neo4j
# 推荐版本：Neo4j Community Edition 5.13.0

version: '3.8'

services:
  neo4j:
    image: neo4j:5.13-community
    container_name: neo4j-kg-mvp
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["apoc"]
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
"""
    with open("knowledge-graph-mvp/docker-compose.yml", "w") as f:
        f.write(content)
    print("✅ 创建文件: docker-compose.yml (可选，支持Docker部署)")

def create_env_file():
    """创建.env文件"""
    content = """# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password123

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# Streamlit配置
STREAMLIT_PORT=8501

# LLM配置 - 默认使用ModelScope
DEFAULT_LLM_PROVIDER=modelscope

# 智谱AI配置
ZHIPU_API_KEY=your_zhipu_api_key_here
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4/
ZHIPU_MODEL_NAME=GLM-4-Flash

# ModelScope配置
MODELSCOPE_API_KEY=your_modelscope_api_key_here
MODELSCOPE_BASE_URL=https://api-inference.modelscope.cn/v1/
MODELSCOPE_MODEL_NAME=Qwen/Qwen3-235B-A22B

# Ollama配置
OLLAMA_API_KEY=ollama
OLLAMA_BASE_URL=http://localhost:11434/v1
OLLAMA_MODEL_NAME=qwen3:1.7b

# OpenRouter配置
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL_NAME=qwen/qwen3-30b-a3b:free
"""
    with open("knowledge-graph-mvp/.env", "w") as f:
        f.write(content)
    print("✅ 创建文件: .env")

def create_llm_config():
    """创建LLM配置文件"""
    content = """{
  "models": {
    "zhipu": {
      "provider": "zhipu",
      "api_key": "your_zhipu_api_key_here",
      "base_url": "https://open.bigmodel.cn/api/paas/v4/",
      "model_name": "GLM-4-Flash",
      "description": "智谱 GLM-4-Flash 模型"
    },
    "modelscope": {
      "provider": "modelscope",
      "api_key": "your_modelscope_api_key_here",
      "base_url": "https://api-inference.modelscope.cn/v1/",
      "model_name": "Qwen/Qwen3-235B-A22B",
      "description": "ModelScope Qwen 模型"
    },
    "ollama": {
      "provider": "ollama",
      "api_key": "ollama",
      "base_url": "http://localhost:11434/v1",
      "model_name": "qwen3:1.7b",
      "description": "本地 Ollama 模型"
    },
    "openrouter": {
      "provider": "openrouter",
      "api_key": "your_openrouter_api_key_here",
      "base_url": "https://openrouter.ai/api/v1",
      "model_name": "qwen/qwen3-30b-a3b:free",
      "description": "OpenRouter qwen3 30B 模型"
    }
  },
  "default_provider": "modelscope"
}"""
    with open("knowledge-graph-mvp/llm_config.json", "w", encoding='utf-8') as f:
        f.write(content)
    print("✅ 创建文件: llm_config.json")

def create_main_py():
    """创建主应用文件"""
    content = '''from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict
import os
from dotenv import load_dotenv

from services.llm_service import LLMService
from services.graph_service import GraphService

load_dotenv()

app = FastAPI(title="知识图谱MVP API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
llm_service = LLMService()
graph_service = GraphService()

class DocumentRequest(BaseModel):
    text: str
    provider: str = "modelscope"

class QueryRequest(BaseModel):
    question: str
    provider: str = "modelscope"

@app.post("/api/documents/extract")
async def extract_knowledge(request: DocumentRequest):
    """从文档中提取知识图谱"""
    try:
        # 1. 使用LLM提取实体和关系
        extracted_data = llm_service.extract_entities_relations(request.text, request.provider)

        # 2. 存储到图数据库
        for entity in extracted_data.get("entities", []):
            graph_service.add_entity(entity)

        for relation in extracted_data.get("relations", []):
            graph_service.add_relation(relation)

        return {
            "status": "success",
            "entities": extracted_data.get("entities", []),
            "relations": extracted_data.get("relations", []),
            "provider_used": request.provider
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/graph/visualize")
async def get_graph_visualization():
    """获取图谱可视化数据"""
    try:
        graph_data = graph_service.get_all_nodes_and_edges()
        return graph_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/graph/query")
async def query_graph(request: QueryRequest):
    """智能查询图谱"""
    try:
        # 简单的查询实现
        answer = llm_service.answer_question(request.question, request.provider)
        return {
            "answer": answer,
            "provider_used": request.provider
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/llm/providers")
async def get_llm_providers():
    """获取可用的LLM提供商"""
    try:
        providers = llm_service.list_available_providers()
        provider_info = {}
        for provider in providers:
            provider_info[provider] = llm_service.get_provider_info(provider)

        return {
            "providers": providers,
            "default": llm_service.default_provider,
            "info": provider_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''
    with open("knowledge-graph-mvp/src/main.py", "w", encoding='utf-8') as f:
        f.write(content)
    print("✅ 创建文件: src/main.py")

def create_llm_service():
    """创建LLM服务文件"""
    content = '''import requests
import json
from typing import List, Dict, Optional
import os
from pathlib import Path

class LLMService:
    def __init__(self, config_path: str = "llm_config.json"):
        """初始化LLM服务"""
        self.config = self.load_config(config_path)
        self.default_provider = self.config.get("default_provider", "modelscope")

    def load_config(self, config_path: str) -> Dict:
        """加载LLM配置"""
        try:
            # 尝试从项目根目录加载配置
            config_file = Path(config_path)
            if not config_file.exists():
                config_file = Path(__file__).parent.parent.parent / config_path

            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载LLM配置失败: {e}")
            # 返回默认配置
            return {
                "models": {
                    "ollama": {
                        "provider": "ollama",
                        "api_key": "ollama",
                        "base_url": "http://localhost:11434/v1",
                        "model_name": "qwen3:1.7b",
                        "description": "本地 Ollama 模型"
                    }
                },
                "default_provider": "ollama"
            }

    def get_model_config(self, provider: Optional[str] = None) -> Dict:
        """获取模型配置"""
        provider = provider or self.default_provider
        return self.config["models"].get(provider, self.config["models"]["ollama"])

    def call_openai_compatible_api(self, messages: List[Dict], provider: Optional[str] = None) -> str:
        """调用OpenAI兼容的API"""
        model_config = self.get_model_config(provider)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {model_config['api_key']}"
        }

        payload = {
            "model": model_config["model_name"],
            "messages": messages,
            "temperature": 0.1,
            "max_tokens": 2000
        }

        try:
            response = requests.post(
                f"{model_config['base_url']}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                print(f"API调用失败: {response.status_code} - {response.text}")
                return ""

        except Exception as e:
            print(f"LLM API调用错误: {e}")
            return ""

    def extract_entities_relations(self, text: str, provider: Optional[str] = None) -> Dict:
        """从文本中提取实体和关系"""
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的知识图谱构建助手。请从给定文本中提取实体和关系，并以JSON格式返回。"
            },
            {
                "role": "user",
                "content": f"""
请从以下文本中提取实体和关系，以JSON格式返回：

文本：{text}

请严格按照以下格式返回，不要添加任何其他内容：
{{
    "entities": [
        {{"name": "实体名", "type": "实体类型", "description": "简短描述"}}
    ],
    "relations": [
        {{"source": "源实体", "target": "目标实体", "relation": "关系类型"}}
    ]
}}

注意：
1. 实体类型可以是：人物、地点、组织、概念、事件等
2. 关系类型可以是：工作于、位于、属于、合作、管理等
3. 只返回JSON，不要其他解释
"""
            }
        ]

        response_text = self.call_openai_compatible_api(messages, provider)

        if not response_text:
            return {"entities": [], "relations": []}

        # 尝试解析JSON
        try:
            # 查找JSON部分
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            if start_idx != -1 and end_idx != 0:
                json_text = response_text[start_idx:end_idx]
                extracted_data = json.loads(json_text)

                # 验证数据格式
                if "entities" in extracted_data and "relations" in extracted_data:
                    return extracted_data
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print(f"原始响应: {response_text}")

        # 如果解析失败，返回空结果
        return {"entities": [], "relations": []}

    def answer_question(self, question: str, provider: Optional[str] = None) -> str:
        """回答问题"""
        messages = [
            {
                "role": "system",
                "content": "你是一个知识图谱问答助手。请根据用户的问题提供准确、简洁的答案。"
            },
            {
                "role": "user",
                "content": f"请回答以下问题：{question}"
            }
        ]

        response = self.call_openai_compatible_api(messages, provider)
        return response or "抱歉，无法回答这个问题。"

    def list_available_providers(self) -> List[str]:
        """列出可用的LLM提供商"""
        return list(self.config["models"].keys())

    def get_provider_info(self, provider: str) -> Dict:
        """获取提供商信息"""
        return self.config["models"].get(provider, {})
'''
    with open("knowledge-graph-mvp/src/services/llm_service.py", "w", encoding='utf-8') as f:
        f.write(content)
    print("✅ 创建文件: src/services/llm_service.py")

def create_graph_service():
    """创建图数据库服务文件"""
    content = '''from neo4j import GraphDatabase
from typing import List, Dict
import os

class GraphService:
    def __init__(self, uri: str = None, user: str = None, password: str = None):
        self.uri = uri or os.getenv("NEO4J_URI", "bolt://localhost:7687")
        self.user = user or os.getenv("NEO4J_USER", "neo4j")
        self.password = password or os.getenv("NEO4J_PASSWORD", "password123")
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
            # 测试连接
            with self.driver.session() as session:
                session.run("RETURN 1")
            print("✅ Neo4j连接成功")
        except Exception as e:
            print(f"❌ Neo4j连接失败: {e}")
            self.driver = None
    
    def add_entity(self, entity: Dict):
        """添加实体到图数据库"""
        if not self.driver:
            return
            
        with self.driver.session() as session:
            session.run(
                "MERGE (e:Entity {name: $name}) SET e.type = $type, e.description = $description",
                name=entity["name"],
                type=entity.get("type", "Unknown"),
                description=entity.get("description", "")
            )
    
    def add_relation(self, relation: Dict):
        """添加关系到图数据库"""
        if not self.driver:
            return
            
        with self.driver.session() as session:
            session.run(
                """
                MERGE (a:Entity {name: $source})
                MERGE (b:Entity {name: $target})
                MERGE (a)-[r:RELATED {type: $relation}]->(b)
                """,
                source=relation["source"],
                target=relation["target"],
                relation=relation["relation"]
            )
    
    def get_all_nodes_and_edges(self) -> Dict:
        """获取所有节点和边用于可视化"""
        if not self.driver:
            return {"nodes": [], "edges": []}
            
        with self.driver.session() as session:
            # 获取节点
            nodes_result = session.run("MATCH (n:Entity) RETURN n")
            nodes = []
            for record in nodes_result:
                node = record["n"]
                nodes.append({
                    "id": node["name"], 
                    "label": node["name"],
                    "type": node.get("type", "Unknown")
                })
            
            # 获取边
            edges_result = session.run(
                "MATCH (a:Entity)-[r:RELATED]->(b:Entity) RETURN a.name, b.name, r.type"
            )
            edges = []
            for record in edges_result:
                edges.append({
                    "from": record["a.name"], 
                    "to": record["b.name"],
                    "label": record["r.type"]
                })
            
            return {"nodes": nodes, "edges": edges}
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
'''
    with open("knowledge-graph-mvp/src/services/graph_service.py", "w", encoding='utf-8') as f:
        f.write(content)
    print("✅ 创建文件: src/services/graph_service.py")

def create_streamlit_app():
    """创建Streamlit前端应用"""
    content = '''import streamlit as st
import requests
import json
from pyvis.network import Network
import tempfile
import os

st.set_page_config(page_title="知识图谱MVP", layout="wide")

# 配置API地址
API_BASE_URL = "http://localhost:8000"

st.title("🧠 知识图谱构建系统 MVP")
st.markdown("---")

# 侧边栏导航
page = st.sidebar.selectbox("选择功能", ["📄 文档上传", "🔍 图谱可视化", "💬 智能查询"])

if page == "📄 文档上传":
    st.header("📄 文档上传和处理")

    # 示例文本
    example_text = """张三是一名软件工程师，在北京的科技公司工作。
他的同事李四是产品经理，负责产品设计。
张三和李四经常合作开发新产品。
公司位于中关村，是一家专注于人工智能的创业公司。"""

    # 文件上传或文本输入
    input_method = st.radio("选择输入方式", ["上传文件", "直接输入文本"])

    content = ""
    if input_method == "上传文件":
        uploaded_file = st.file_uploader("选择文本文件", type=['txt', 'md'])
        if uploaded_file is not None:
            content = uploaded_file.read().decode('utf-8')
    else:
        content = st.text_area("输入文本内容", value=example_text, height=150)

    if content:
        st.text_area("文本内容预览", content, height=100)

        if st.button("🚀 提取知识图谱", type="primary"):
            with st.spinner("正在提取实体和关系..."):
                try:
                    # 获取选择的LLM提供商
                    provider = st.session_state.get('selected_provider', 'modelscope')

                    response = requests.post(
                        f"{API_BASE_URL}/api/documents/extract",
                        json={"text": content, "provider": provider},
                        timeout=60
                    )

                    if response.status_code == 200:
                        result = response.json()
                        entities = result.get('entities', [])
                        relations = result.get('relations', [])

                        st.success(f"✅ 提取完成！发现 {len(entities)} 个实体，{len(relations)} 个关系")

                        # 显示提取结果
                        col1, col2 = st.columns(2)
                        with col1:
                            st.subheader("🏷️ 实体")
                            if entities:
                                for entity in entities:
                                    st.write(f"- **{entity['name']}** ({entity.get('type', 'Unknown')})")
                            else:
                                st.info("未发现实体")

                        with col2:
                            st.subheader("🔗 关系")
                            if relations:
                                for relation in relations:
                                    st.write(f"- {relation['source']} → {relation['target']} ({relation['relation']})")
                            else:
                                st.info("未发现关系")
                    else:
                        st.error(f"❌ 提取失败: {response.text}")
                except requests.exceptions.RequestException as e:
                    st.error(f"❌ 连接失败: {str(e)}")
                    st.info("请确保后端API服务正在运行 (http://localhost:8000)")

elif page == "🔍 图谱可视化":
    st.header("🔍 知识图谱可视化")

    if st.button("📊 加载图谱", type="primary"):
        try:
            response = requests.get(f"{API_BASE_URL}/api/graph/visualize", timeout=30)

            if response.status_code == 200:
                graph_data = response.json()
                nodes = graph_data.get('nodes', [])
                edges = graph_data.get('edges', [])

                if nodes:
                    st.success(f"✅ 加载成功！共 {len(nodes)} 个节点，{len(edges)} 条边")

                    # 创建网络图
                    net = Network(height="600px", width="100%", bgcolor="#222222", font_color="white")
                    net.set_options("""
                    var options = {
                      "physics": {
                        "enabled": true,
                        "stabilization": {"iterations": 100}
                      }
                    }
                    """)

                    # 添加节点
                    for node in nodes:
                        net.add_node(
                            node['id'],
                            label=node['label'],
                            color="#97C2FC",
                            title=f"类型: {node.get('type', 'Unknown')}"
                        )

                    # 添加边
                    for edge in edges:
                        net.add_edge(
                            edge['from'],
                            edge['to'],
                            label=edge['label'],
                            color="#848484"
                        )

                    # 保存并显示
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".html") as tmp:
                        net.save_graph(tmp.name)
                        with open(tmp.name, 'r', encoding='utf-8') as f:
                            html_content = f.read()
                        st.components.v1.html(html_content, height=600)

                        # 清理临时文件
                        os.unlink(tmp.name)

                    # 显示统计信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("节点数量", len(nodes))
                    with col2:
                        st.metric("边数量", len(edges))
                    with col3:
                        entity_types = set(node.get('type', 'Unknown') for node in nodes)
                        st.metric("实体类型", len(entity_types))

                else:
                    st.info("📝 暂无图谱数据，请先上传文档进行知识提取")
            else:
                st.error(f"❌ 加载失败: {response.text}")
        except requests.exceptions.RequestException as e:
            st.error(f"❌ 连接失败: {str(e)}")
            st.info("请确保后端API服务正在运行")

elif page == "💬 智能查询":
    st.header("💬 智能查询")

    # 示例问题
    example_questions = [
        "张三是做什么工作的？",
        "张三和李四是什么关系？",
        "公司在哪里？",
        "有哪些人物？"
    ]

    st.subheader("💡 示例问题")
    for i, question in enumerate(example_questions):
        if st.button(f"❓ {question}", key=f"example_{i}"):
            st.session_state.query_input = question

    # 查询输入
    query = st.text_input(
        "输入您的问题",
        value=st.session_state.get('query_input', ''),
        placeholder="例如：张三和李四是什么关系？"
    )

    if st.button("🔍 查询", type="primary") and query:
        with st.spinner("正在查询..."):
            try:
                # 获取选择的LLM提供商
                provider = st.session_state.get('selected_provider', 'modelscope')

                response = requests.post(
                    f"{API_BASE_URL}/api/graph/query",
                    json={"question": query, "provider": provider},
                    timeout=60
                )

                if response.status_code == 200:
                    result = response.json()
                    answer = result.get('answer', '无法获取答案')

                    st.subheader("🤖 AI回答")
                    st.write(answer)
                else:
                    st.error(f"❌ 查询失败: {response.text}")
            except requests.exceptions.RequestException as e:
                st.error(f"❌ 连接失败: {str(e)}")
                st.info("请确保后端API服务正在运行")

# 侧边栏信息
st.sidebar.markdown("---")
st.sidebar.markdown("### 📋 系统状态")

# 检查服务状态
try:
    health_response = requests.get(f"{API_BASE_URL}/health", timeout=5)
    if health_response.status_code == 200:
        st.sidebar.success("✅ API服务正常")
    else:
        st.sidebar.error("❌ API服务异常")
except:
    st.sidebar.error("❌ API服务未连接")

# LLM提供商选择
st.sidebar.markdown("### 🤖 LLM设置")
try:
    providers_response = requests.get(f"{API_BASE_URL}/api/llm/providers", timeout=5)
    if providers_response.status_code == 200:
        providers = providers_response.json()
        selected_provider = st.sidebar.selectbox(
            "选择LLM提供商",
            providers.get("providers", ["modelscope"]),
            index=providers.get("providers", ["modelscope"]).index(providers.get("default", "modelscope"))
        )

        # 显示当前提供商信息
        if selected_provider:
            provider_info = providers.get("info", {}).get(selected_provider, {})
            st.sidebar.info(f"**{selected_provider}**: {provider_info.get('description', '未知模型')}")

            # 存储选择的提供商到session state
            st.session_state.selected_provider = selected_provider
    else:
        st.sidebar.warning("⚠️ 无法获取LLM提供商信息")
        st.session_state.selected_provider = "modelscope"
except:
    st.sidebar.warning("⚠️ LLM服务连接失败")
    st.session_state.selected_provider = "modelscope"

st.sidebar.markdown("### 🔗 相关链接")
st.sidebar.markdown("- [API文档](http://localhost:8000/docs)")
st.sidebar.markdown("- [Neo4j浏览器](http://localhost:7474)")

st.sidebar.markdown("### ℹ️ 使用说明")
st.sidebar.markdown("""
1. **文档上传**: 上传文本文件或直接输入文本
2. **图谱可视化**: 查看提取的知识图谱
3. **智能查询**: 用自然语言查询图谱
""")
'''
    with open("knowledge-graph-mvp/frontend/app.py", "w", encoding='utf-8') as f:
        f.write(content)
    print("✅ 创建文件: frontend/app.py")

def create_readme():
    """创建README文件"""
    content = '''# 知识图谱构建系统 MVP

## 🎯 项目简介

这是一个最小可用的知识图谱构建系统，能够从文本文档中提取实体和关系，构建知识图谱，并提供可视化和查询功能。

## ✨ 主要功能

- 📄 文本文档上传和解析
- 🤖 基于LLM的实体关系提取
- 🗄️ Neo4j图数据库存储
- 🔍 交互式图谱可视化
- 💬 自然语言查询

## 🛠️ 技术栈

- **后端**: FastAPI + Python
- **前端**: Streamlit
- **数据库**: Neo4j
- **LLM**: 支持多种提供商（智谱AI、ModelScope、Ollama、OpenRouter）
- **可视化**: Pyvis
- **包管理**: uv (现代化Python包管理器)

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装uv包管理器（如果未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh  # Linux/Mac
# 或 Windows: powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 同步依赖（自动创建虚拟环境）
uv sync
```

### 2. 启动服务

```bash
# 启动Neo4j数据库
docker-compose up -d neo4j

# 安装并启动Ollama
# 下载：https://ollama.ai/download
ollama pull llama3.1:8b
```

### 3. 启动应用

```bash
# 方式1：一键启动（推荐）
./start.sh  # Linux/Mac
# 或 start.bat  # Windows

# 方式2：手动启动
# 启动后端API
cd src
uv run uvicorn main:app --reload --port 8000

# 启动前端界面（新终端）
cd frontend
uv run streamlit run app.py --server.port 8501
```

### 4. 访问应用

- 前端界面: http://localhost:8501
- API文档: http://localhost:8000/docs
- Neo4j浏览器: http://localhost:7474 (用户名: neo4j, 密码: password123)

## 📁 项目结构

```
knowledge-graph-mvp/
├── src/                    # 后端源码
│   ├── main.py            # FastAPI应用入口
│   └── services/          # 业务逻辑
├── frontend/              # 前端源码
│   └── app.py            # Streamlit应用
├── pyproject.toml         # 项目配置和依赖
├── uv.lock               # 依赖锁定文件
├── docker-compose.yml     # Docker配置
└── .env                  # 环境变量
```

## 🔧 配置说明

主要配置在 `pyproject.toml` 和 `.env` 文件中：

**pyproject.toml** - 项目依赖和uv配置：
```toml
[project]
dependencies = [
    "fastapi>=0.104.1",
    "streamlit>=1.28.1",
    # ... 其他依赖
]

# 配置阿里镜像源
[[tool.uv.index]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true
```

**.env** - 运行时环境变量：
```env
# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password123

# LLM配置
DEFAULT_LLM_PROVIDER=modelscope
ZHIPU_API_KEY=your_zhipu_api_key_here
MODELSCOPE_API_KEY=your_modelscope_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

**llm_config.json** - LLM提供商配置：
```json
{
  "models": {
    "zhipu": {
      "provider": "zhipu",
      "api_key": "your_api_key",
      "base_url": "https://open.bigmodel.cn/api/paas/v4/",
      "model_name": "GLM-4-Flash"
    },
    "modelscope": {
      "provider": "modelscope",
      "api_key": "your_api_key",
      "base_url": "https://api-inference.modelscope.cn/v1/",
      "model_name": "Qwen/Qwen3-235B-A22B"
    }
  },
  "default_provider": "modelscope"
}
```

## 📝 使用指南

1. **配置LLM**: 在侧边栏选择LLM提供商（智谱AI、ModelScope、Ollama等）
2. **上传文档**: 在前端选择"文档上传"，上传文本文件或直接输入文本
3. **提取知识**: 点击"提取知识图谱"按钮，系统会使用选择的LLM提取实体和关系
4. **查看图谱**: 切换到"图谱可视化"页面，查看提取的知识图谱
5. **智能查询**: 在"智能查询"页面用自然语言提问

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8000
   lsof -i :8501
   ```

2. **Neo4j连接失败**
   ```bash
   # 重启Neo4j
   docker-compose restart neo4j
   ```

3. **Ollama连接失败**
   ```bash
   # 检查Ollama状态
   ollama list
   ollama serve
   ```

## 📈 后续扩展

- [ ] 支持更多文档格式（PDF、Word等）
- [ ] 优化实体关系提取质量
- [ ] 增加复杂查询功能
- [ ] 添加用户认证和权限管理
- [ ] 性能优化和监控

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
'''
    with open("knowledge-graph-mvp/README.md", "w", encoding='utf-8') as f:
        f.write(content)
    print("✅ 创建文件: README.md")

def create_start_scripts():
    """创建启动脚本"""
    # Linux/Mac启动脚本
    start_sh_content = '''#!/bin/bash

echo "🚀 启动知识图谱MVP系统..."

# 检查uv是否安装
if ! command -v uv &> /dev/null; then
    echo "📦 安装uv包管理器..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source $HOME/.cargo/env
fi

# 检查Neo4j连接（本地或Docker）
echo "🗄️ 检查Neo4j服务..."
if ! nc -z localhost 7687 2>/dev/null; then
    echo "❌ Neo4j服务未启动，请选择以下方式之一："
    echo ""
    echo "方式1：本地安装Neo4j（推荐开发环境）"
    echo "  1. 下载Neo4j Desktop: https://neo4j.com/download/"
    echo "  2. 或安装Neo4j Community 5.13.0"
    echo "  3. 启动Neo4j服务"
    echo ""
    echo "方式2：使用Docker"
    if command -v docker &> /dev/null; then
        echo "  docker-compose up -d neo4j"
    else
        echo "  请先安装Docker，然后运行: docker-compose up -d neo4j"
    fi
    echo ""
    echo "启动Neo4j后重新运行此脚本"
    exit 1
else
    echo "✅ Neo4j服务正在运行"
fi

# 配置阿里镜像源
export UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
export UV_EXTRA_INDEX_URL=https://pypi.org/simple/

# 同步依赖
echo "📦 同步Python依赖..."
uv sync

# 检查Neo4j服务状态
echo "🗄️ 验证Neo4j连接..."
if ! nc -z localhost 7687 2>/dev/null; then
    echo "❌ 无法连接到Neo4j，请确保Neo4j服务正在运行"
    echo "💡 如需启动Docker版本: docker-compose up -d neo4j"
    exit 1
fi
echo "✅ Neo4j连接正常"

# 检查Ollama（可选）
if command -v ollama &> /dev/null; then
    echo "🤖 检查Ollama模型..."
    ollama pull qwen3:1.7b
else
    echo "ℹ️ Ollama 未安装，将使用在线LLM服务"
    echo "如需使用本地模型，请安装Ollama："
    echo "下载地址：https://ollama.ai/download"
    echo "安装后运行：ollama pull qwen3:1.7b"
fi

# 启动后端API
echo "🔧 启动后端API..."
cd src
uv run uvicorn main:app --reload --port 8000 &
API_PID=$!

# 等待API启动
sleep 5

# 启动前端
echo "🌐 启动前端界面..."
cd ../frontend
uv run streamlit run app.py --server.port 8501 &
FRONTEND_PID=$!

echo "✅ 系统启动完成！"
echo "📊 前端界面: http://localhost:8501"
echo "🔧 API文档: http://localhost:8000/docs"
echo "🗄️ Neo4j浏览器: http://localhost:7474"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '🛑 正在停止服务...'; kill $API_PID $FRONTEND_PID; docker-compose down; exit" INT
wait
'''

    with open("knowledge-graph-mvp/start.sh", "w", encoding='utf-8') as f:
        f.write(start_sh_content)

    # 给脚本执行权限
    import stat
    import os
    os.chmod("knowledge-graph-mvp/start.sh", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)

    # Windows启动脚本
    start_bat_content = '''@echo off
echo 🚀 启动知识图谱MVP系统...

REM 检查uv是否安装
uv --version >nul 2>&1
if errorlevel 1 (
    echo 📦 安装uv包管理器...
    powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
)

REM 配置阿里镜像源
set UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
set UV_EXTRA_INDEX_URL=https://pypi.org/simple/

REM 同步依赖
echo 📦 同步Python依赖...
uv sync

REM 检查Neo4j服务状态
echo 🗄️ 验证Neo4j连接...
netstat -an | find "7687" >nul
if errorlevel 1 (
    echo ❌ 无法连接到Neo4j，请确保Neo4j服务正在运行
    echo 💡 如需启动Docker版本: docker-compose up -d neo4j
    pause
    exit /b 1
)
echo ✅ Neo4j连接正常

REM 启动后端API
echo 🔧 启动后端API...
start "API Server" cmd /k "cd src && uv run uvicorn main:app --reload --port 8000"

REM 等待API启动
timeout /t 5 /nobreak

REM 启动前端
echo 🌐 启动前端界面...
start "Frontend" cmd /k "cd frontend && uv run streamlit run app.py --server.port 8501"

echo ✅ 系统启动完成！
echo 📊 前端界面: http://localhost:8501
echo 🔧 API文档: http://localhost:8000/docs
echo 🗄️ Neo4j浏览器: http://localhost:7474
pause
'''

    with open("knowledge-graph-mvp/start.bat", "w", encoding='utf-8') as f:
        f.write(start_bat_content)

    print("✅ 创建文件: start.sh")
    print("✅ 创建文件: start.bat")

def create_init_files():
    """创建__init__.py文件"""
    init_files = [
        "knowledge-graph-mvp/src/__init__.py",
        "knowledge-graph-mvp/src/models/__init__.py",
        "knowledge-graph-mvp/src/services/__init__.py",
        "knowledge-graph-mvp/src/api/__init__.py",
        "knowledge-graph-mvp/src/utils/__init__.py",
    ]
    
    for init_file in init_files:
        with open(init_file, "w") as f:
            f.write("# This file makes Python treat the directory as a package\n")
        print(f"✅ 创建文件: {init_file}")

def main():
    """主函数"""
    print("🚀 开始创建知识图谱MVP项目...")
    print("=" * 50)
    
    # 检查当前目录
    if os.path.exists("knowledge-graph-mvp"):
        response = input("⚠️ 项目目录已存在，是否覆盖？(y/N): ")
        if response.lower() != 'y':
            print("❌ 取消创建")
            return
        
        import shutil
        shutil.rmtree("knowledge-graph-mvp")
        print("🗑️ 删除现有项目目录")
    
    # 创建项目结构
    create_directory_structure()
    create_pyproject_toml()
    create_docker_compose()
    create_env_file()
    create_llm_config()
    create_init_files()
    create_main_py()
    create_llm_service()
    create_graph_service()
    create_streamlit_app()
    create_readme()
    create_start_scripts()

    print("=" * 50)
    print("✅ 项目创建完成！")
    print("\n📋 下一步操作：")
    print("1. cd knowledge-graph-mvp")
    print("2. 安装Neo4j数据库：")
    print("   方式A：下载Neo4j Desktop (推荐): https://neo4j.com/download/")
    print("   方式B：使用Docker: docker-compose up -d neo4j")
    print("3. 启动Neo4j服务（Desktop点击Start或命令行启动）")
    print("4. ./start.sh  # Linux/Mac 或 start.bat  # Windows")
    print("   （脚本会自动安装uv、同步依赖、启动服务）")
    print("\n🌐 访问地址：")
    print("- 前端界面: http://localhost:8501")
    print("- API文档: http://localhost:8000/docs")
    print("- Neo4j浏览器: http://localhost:7474")
    print("\n📚 详细说明请查看：")
    print("- mvp-implementation-guide.md - 完整实现指南")
    print("- neo4j-local-installation.md - Neo4j本地安装指南")
    print("- llm-configuration-guide.md - LLM配置指南")
    print("\n🚀 系统优势：")
    print("- uv包管理器：更快的依赖解析和安装")
    print("- 多LLM支持：智谱AI、ModelScope、Ollama、OpenRouter")
    print("- 灵活部署：支持本地Neo4j或Docker部署")
    print("- 双镜像源：阿里云+清华，确保下载稳定")

if __name__ == "__main__":
    main()
