# uv包管理器使用指南

## 🚀 什么是uv？

uv是由Astral团队开发的现代化Python包管理器，用Rust编写，速度比pip快10-100倍。它是pip、pip-tools、pipx、poetry、pyenv、virtualenv等工具的统一替代品。

## 📦 安装uv

### Linux/Mac
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Windows
```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 验证安装
```bash
uv --version
```

## 🔧 基本使用

### 项目初始化
```bash
# 创建新项目
uv init my-project
cd my-project

# 在现有项目中初始化
uv init
```

### 依赖管理
```bash
# 添加依赖
uv add fastapi
uv add "streamlit>=1.28.0"

# 添加开发依赖
uv add --dev pytest black

# 移除依赖
uv remove fastapi

# 同步依赖（安装pyproject.toml中的所有依赖）
uv sync

# 重新安装所有依赖
uv sync --reinstall
```

### 虚拟环境管理
```bash
# uv会自动创建和管理虚拟环境，无需手动操作

# 查看当前环境信息
uv info

# 清理环境
uv clean
```

### 运行命令
```bash
# 在虚拟环境中运行命令
uv run python script.py
uv run uvicorn main:app --reload
uv run streamlit run app.py

# 运行开发工具
uv run pytest
uv run black .
```

## 🌏 配置国内镜像源

### 方法1：在pyproject.toml中配置（推荐）
```toml
# pyproject.toml
# 配置多个镜像源，提高下载成功率
[[tool.uv.index]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"
```

### 方法2：环境变量配置
```bash
# Linux/Mac - 设置主镜像源和备用源
export UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
export UV_EXTRA_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple/

# Windows - 设置主镜像源和备用源
set UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
set UV_EXTRA_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 方法3：命令行参数
```bash
uv add fastapi --index-url https://mirrors.aliyun.com/pypi/simple/
```

## 📋 常用命令对比

| 功能 | pip | uv |
|------|-----|-----|
| 安装包 | `pip install package` | `uv add package` |
| 卸载包 | `pip uninstall package` | `uv remove package` |
| 安装依赖 | `pip install -r requirements.txt` | `uv sync` |
| 创建虚拟环境 | `python -m venv venv` | 自动创建 |
| 激活环境 | `source venv/bin/activate` | 自动管理 |
| 运行命令 | `python script.py` | `uv run python script.py` |
| 查看已安装包 | `pip list` | `uv list` |
| 更新包 | `pip install --upgrade package` | `uv add package --upgrade` |

## 🔍 项目文件说明

### pyproject.toml
```toml
[project]
name = "knowledge-graph-mvp"
version = "0.1.0"
description = "知识图谱构建系统MVP"
dependencies = [
    "fastapi>=0.104.1",
    "streamlit>=1.28.1",
    # ... 其他依赖
]
requires-python = ">=3.8"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "black>=23.0.0",
]

# 镜像源配置
[[tool.uv.index]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true
```

### uv.lock
- 自动生成的锁定文件
- 记录确切的依赖版本
- 确保环境一致性
- 类似于npm的package-lock.json

## 🚀 高级功能

### 缓存管理
```bash
# 查看缓存信息
uv cache info

# 清理缓存
uv cache clean

# 清理特定包的缓存
uv cache clean package-name
```

### 多Python版本管理
```bash
# 指定Python版本
uv add --python 3.9 package

# 查看可用Python版本
uv python list
```

### 脚本运行
```bash
# 运行Python脚本
uv run python -c "print('Hello, World!')"

# 运行模块
uv run -m pytest

# 运行可执行文件
uv run uvicorn main:app --reload
```

## 🔧 故障排除

### 常见问题

1. **uv命令未找到**
   ```bash
   # 重新加载shell配置
   source ~/.bashrc  # 或 ~/.zshrc
   
   # 或重新安装
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **依赖解析失败**
   ```bash
   # 清理缓存重试
   uv cache clean
   uv sync
   
   # 使用详细输出查看错误
   uv sync -v
   ```

3. **镜像源连接问题**
   ```bash
   # 临时使用官方源
   uv sync --index-url https://pypi.org/simple/
   
   # 检查网络连接
   curl -I https://mirrors.aliyun.com/pypi/simple/
   ```

4. **虚拟环境问题**
   ```bash
   # 重新创建环境
   uv clean
   uv sync
   ```

## 📈 性能对比

| 操作 | pip | uv | 提升倍数 |
|------|-----|-----|----------|
| 依赖解析 | 30s | 0.3s | 100x |
| 包安装 | 45s | 2s | 22x |
| 环境创建 | 15s | 1s | 15x |
| 缓存命中 | 10s | 0.1s | 100x |

## 🎯 最佳实践

### 1. 项目结构
```
my-project/
├── pyproject.toml    # 项目配置
├── uv.lock          # 锁定文件（提交到版本控制）
├── src/             # 源代码
└── tests/           # 测试代码
```

### 2. 依赖管理
- 使用语义化版本约束：`>=1.0.0,<2.0.0`
- 区分生产依赖和开发依赖
- 定期更新依赖：`uv sync --upgrade`

### 3. 团队协作
- 提交`pyproject.toml`和`uv.lock`到版本控制
- 团队成员使用`uv sync`保持环境一致
- 在CI/CD中使用uv加速构建

### 4. 部署优化
```bash
# 生产环境只安装必要依赖
uv sync --no-dev

# 使用缓存加速Docker构建
COPY pyproject.toml uv.lock ./
RUN uv sync --no-dev
```

## 🔗 相关资源

- [uv官方文档](https://docs.astral.sh/uv/)
- [uv GitHub仓库](https://github.com/astral-sh/uv)
- [Python包管理最佳实践](https://packaging.python.org/)

## 💡 小贴士

1. **自动补全**: 运行`uv generate-shell-completion bash >> ~/.bashrc`启用命令补全
2. **配置文件**: uv会读取`pyproject.toml`、`uv.toml`等配置文件
3. **环境隔离**: 每个项目自动使用独立的虚拟环境
4. **快速安装**: 利用并行下载和缓存机制大幅提升安装速度
5. **兼容性**: 完全兼容pip和PyPI生态系统

使用uv可以显著提升Python项目的开发效率，特别是在依赖管理和环境配置方面。对于知识图谱MVP项目，uv的快速安装和自动环境管理特性能让开发者更专注于核心功能的实现。
