# 知识图谱构建系统 MVP 实现指南

## 🎯 MVP 目标

构建一个**最小可用的知识图谱系统**，能够：
1. 从文本文档中提取实体和关系
2. 构建简单的知识图谱
3. 提供基本的图谱查询功能
4. 具备简单的Web界面

**预计开发时间**：2-3周
**技术难度**：⭐⭐⭐（中等）

## 📋 MVP 功能范围

### ✅ 包含功能
- [x] 文本文档上传和解析
- [x] 基于LLM的实体关系提取
- [x] Neo4j图数据库存储
- [x] 简单的图谱可视化
- [x] 基本的自然语言查询

### ❌ 暂不包含
- [ ] 多模态处理（图像、表格等）
- [ ] 复杂的GraphRAG功能
- [ ] 企业级安全和权限
- [ ] 性能优化和监控
- [ ] 多Agent协作

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 前端      │    │   Python 后端   │    │   Neo4j 数据库  │
│   (Streamlit)   │◄──►│   (FastAPI)     │◄──►│   (图数据存储)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   LLM 服务      │
                       │   (Ollama)      │
                       └─────────────────┘
```

## 🛠️ 技术栈选择

### 后端框架
- **FastAPI**：现代、快速的Python Web框架
- **理由**：自动生成API文档、类型提示支持、易于学习

### 数据库
- **Neo4j Community Edition**：图数据库
- **理由**：专为图数据设计、Cypher查询语言直观

### LLM服务
- **Ollama**：本地LLM部署工具
- **推荐模型**：llama3.1:8b（平衡性能和资源消耗）

### 前端界面
- **Streamlit**：Python Web应用框架
- **理由**：纯Python开发、快速原型、无需前端技能

### 图可视化
- **Pyvis**：Python图可视化库
- **理由**：简单易用、与Streamlit集成良好

## 📁 项目结构

```
knowledge-graph-mvp/
├── README.md                 # 项目说明
├── requirements.txt          # Python依赖
├── docker-compose.yml        # 容器编排
├── .env                      # 环境变量
├── src/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用入口
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   ├── entities.py      # 实体模型
│   │   └── graph.py         # 图模型
│   ├── services/            # 业务逻辑
│   │   ├── __init__.py
│   │   ├── llm_service.py   # LLM服务
│   │   ├── graph_service.py # 图操作服务
│   │   └── extract_service.py # 提取服务
│   ├── api/                 # API路由
│   │   ├── __init__.py
│   │   ├── documents.py     # 文档API
│   │   └── graph.py         # 图谱API
│   └── utils/               # 工具函数
│       ├── __init__.py
│       └── text_processor.py
├── frontend/
│   ├── app.py              # Streamlit应用
│   ├── pages/              # 页面组件
│   │   ├── upload.py       # 上传页面
│   │   ├── visualize.py    # 可视化页面
│   │   └── query.py        # 查询页面
│   └── components/         # UI组件
│       └── graph_viz.py    # 图可视化组件
└── tests/                  # 测试文件
    ├── test_api.py
    └── test_services.py
```

## 🚀 快速开始指南

### 步骤1：环境准备

```bash
# 1. 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh  # Linux/Mac
# 或 Windows: powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 2. 克隆项目（假设已创建）
git clone <your-repo>
cd knowledge-graph-mvp

# 3. 配置阿里镜像源（可选，提升下载速度）
export UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
export UV_EXTRA_INDEX_URL=https://pypi.org/simple/

# 4. 初始化项目并安装依赖
uv sync
```

### 步骤2：启动基础服务

#### 方式1：本地安装Neo4j（推荐开发环境）
```bash
# 1. 安装Neo4j（选择其中一种方式）
# 方式A：Neo4j Desktop（推荐新手）
# 下载：https://neo4j.com/download/
# 创建数据库，版本选择5.13.0，密码设置为password123

# 方式B：命令行安装
# macOS: brew install neo4j
# Ubuntu: sudo apt install neo4j=1:5.13.0
# Windows: 下载安装包

# 2. 启动Neo4j服务
# Neo4j Desktop: 点击Start按钮
# 命令行: neo4j start 或 sudo systemctl start neo4j

# 3. 验证Neo4j连接
curl http://localhost:7474  # Neo4j浏览器
# 用户名: neo4j, 密码: password123
```

#### 方式2：使用Docker（可选）
```bash
# 如果你更喜欢Docker方式
docker-compose up -d neo4j

# 验证服务
curl http://localhost:7474  # Neo4j浏览器
```

#### 安装LLM服务（可选）
```bash
# 如果要使用本地Ollama
# 下载：https://ollama.ai/download
ollama pull qwen3:1.7b

# 验证Ollama API
curl http://localhost:11434
```

### 步骤3：启动应用

```bash
# 1. 启动后端API
cd src
uv run uvicorn main:app --reload --port 8000

# 2. 启动前端界面（新终端）
cd frontend
uv run streamlit run app.py --server.port 8501
```

### 步骤4：验证功能

1. 打开浏览器访问 `http://localhost:8501`
2. 上传一个文本文件
3. 查看提取的知识图谱
4. 尝试自然语言查询

## 💻 核心代码实现

### 1. LLM服务（src/services/llm_service.py）

```python
import requests
import json
from typing import List, Dict

class LLMService:
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.model = "llama3.1:8b"
    
    def extract_entities_relations(self, text: str) -> Dict:
        """从文本中提取实体和关系"""
        prompt = f"""
        从以下文本中提取实体和关系，以JSON格式返回：
        
        文本：{text}
        
        请返回格式：
        {{
            "entities": [
                {{"name": "实体名", "type": "实体类型", "description": "描述"}}
            ],
            "relations": [
                {{"source": "源实体", "target": "目标实体", "relation": "关系类型"}}
            ]
        }}
        """
        
        response = requests.post(
            f"{self.base_url}/api/generate",
            json={
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
        )
        
        result = response.json()
        try:
            # 解析LLM返回的JSON
            extracted_data = json.loads(result["response"])
            return extracted_data
        except:
            # 如果解析失败，返回空结果
            return {"entities": [], "relations": []}
```

### 2. 图数据库服务（src/services/graph_service.py）

```python
from neo4j import GraphDatabase
from typing import List, Dict

class GraphService:
    def __init__(self, uri: str = "bolt://localhost:7687", 
                 user: str = "neo4j", password: str = "password"):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
    
    def add_entity(self, entity: Dict):
        """添加实体到图数据库"""
        with self.driver.session() as session:
            session.run(
                "MERGE (e:Entity {name: $name, type: $type, description: $description})",
                name=entity["name"],
                type=entity["type"],
                description=entity.get("description", "")
            )
    
    def add_relation(self, relation: Dict):
        """添加关系到图数据库"""
        with self.driver.session() as session:
            session.run(
                """
                MATCH (a:Entity {name: $source})
                MATCH (b:Entity {name: $target})
                MERGE (a)-[r:RELATED {type: $relation}]->(b)
                """,
                source=relation["source"],
                target=relation["target"],
                relation=relation["relation"]
            )
    
    def query_graph(self, query: str) -> List[Dict]:
        """查询图数据库"""
        with self.driver.session() as session:
            result = session.run(query)
            return [record.data() for record in result]
    
    def get_all_nodes_and_edges(self) -> Dict:
        """获取所有节点和边用于可视化"""
        with self.driver.session() as session:
            # 获取节点
            nodes_result = session.run("MATCH (n:Entity) RETURN n")
            nodes = [{"id": record["n"]["name"], 
                     "label": record["n"]["name"],
                     "type": record["n"]["type"]} 
                    for record in nodes_result]
            
            # 获取边
            edges_result = session.run(
                "MATCH (a:Entity)-[r:RELATED]->(b:Entity) RETURN a.name, b.name, r.type"
            )
            edges = [{"from": record["a.name"], 
                     "to": record["b.name"],
                     "label": record["r.type"]} 
                    for record in edges_result]
            
            return {"nodes": nodes, "edges": edges}
```

### 3. Streamlit前端（frontend/app.py）

```python
import streamlit as st
import requests
import json
from pyvis.network import Network
import tempfile

st.set_page_config(page_title="知识图谱MVP", layout="wide")

st.title("🧠 知识图谱构建系统 MVP")

# 侧边栏导航
page = st.sidebar.selectbox("选择功能", ["文档上传", "图谱可视化", "智能查询"])

if page == "文档上传":
    st.header("📄 文档上传和处理")
    
    uploaded_file = st.file_uploader("选择文本文件", type=['txt', 'md'])
    
    if uploaded_file is not None:
        # 读取文件内容
        content = uploaded_file.read().decode('utf-8')
        st.text_area("文件内容预览", content, height=200)
        
        if st.button("提取知识图谱"):
            with st.spinner("正在提取实体和关系..."):
                # 调用后端API
                response = requests.post(
                    "http://localhost:8000/api/documents/extract",
                    json={"text": content}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    st.success(f"提取完成！发现 {len(result['entities'])} 个实体，{len(result['relations'])} 个关系")
                    
                    # 显示提取结果
                    col1, col2 = st.columns(2)
                    with col1:
                        st.subheader("实体")
                        for entity in result['entities']:
                            st.write(f"- **{entity['name']}** ({entity['type']})")
                    
                    with col2:
                        st.subheader("关系")
                        for relation in result['relations']:
                            st.write(f"- {relation['source']} → {relation['target']} ({relation['relation']})")
                else:
                    st.error("提取失败，请检查后端服务")

elif page == "图谱可视化":
    st.header("🔍 知识图谱可视化")
    
    if st.button("加载图谱"):
        # 获取图谱数据
        response = requests.get("http://localhost:8000/api/graph/visualize")
        
        if response.status_code == 200:
            graph_data = response.json()
            
            if graph_data['nodes']:
                # 创建网络图
                net = Network(height="600px", width="100%", bgcolor="#222222", font_color="white")
                
                # 添加节点
                for node in graph_data['nodes']:
                    net.add_node(node['id'], label=node['label'], color="#97C2FC")
                
                # 添加边
                for edge in graph_data['edges']:
                    net.add_edge(edge['from'], edge['to'], label=edge['label'])
                
                # 保存并显示
                with tempfile.NamedTemporaryFile(delete=False, suffix=".html") as tmp:
                    net.save_graph(tmp.name)
                    with open(tmp.name, 'r', encoding='utf-8') as f:
                        html_content = f.read()
                    st.components.v1.html(html_content, height=600)
            else:
                st.info("暂无图谱数据，请先上传文档")
        else:
            st.error("无法加载图谱数据")

elif page == "智能查询":
    st.header("💬 智能查询")
    
    query = st.text_input("输入您的问题", placeholder="例如：张三和李四是什么关系？")
    
    if st.button("查询") and query:
        with st.spinner("正在查询..."):
            response = requests.post(
                "http://localhost:8000/api/graph/query",
                json={"question": query}
            )
            
            if response.status_code == 200:
                result = response.json()
                st.write("**查询结果：**")
                st.write(result['answer'])
            else:
                st.error("查询失败")
```

## 📝 开发步骤详解

### 第1周：基础框架搭建
1. **Day 1-2**：环境搭建和项目结构创建
2. **Day 3-4**：实现LLM服务和基本的实体关系提取
3. **Day 5-7**：集成Neo4j数据库，实现基本的图操作

### 第2周：核心功能开发
1. **Day 8-10**：开发FastAPI后端接口
2. **Day 11-12**：实现Streamlit前端界面
3. **Day 13-14**：集成图可视化功能

### 第3周：功能完善和测试
1. **Day 15-17**：完善查询功能，添加错误处理
2. **Day 18-19**：系统测试和bug修复
3. **Day 20-21**：文档完善和部署优化

## 🔧 常见问题解决

### Q1: Ollama连接失败
```bash
# 检查Ollama是否运行
ollama list

# 重启Ollama服务
ollama serve
```

### Q2: Neo4j连接失败
```bash
# 检查Docker容器状态
docker ps

# 重启Neo4j容器
docker-compose restart neo4j
```

### Q3: 实体提取效果不好
- 优化提示词模板
- 尝试不同的LLM模型
- 增加示例和约束条件

## 🎯 MVP成功标准

- [ ] 能够上传文本文档
- [ ] 成功提取至少70%的明显实体
- [ ] 图谱可视化正常显示
- [ ] 基本查询功能可用
- [ ] 系统运行稳定，无崩溃

## 🚀 后续扩展方向

MVP完成后，可以按以下顺序扩展：
1. **文档格式支持**：PDF、Word等
2. **提取质量优化**：更好的提示词、后处理
3. **查询功能增强**：复杂查询、推理能力
4. **用户体验优化**：更好的界面、交互体验
5. **性能优化**：缓存、批处理等

## 📦 配置文件模板

### pyproject.toml
```toml
[project]
name = "knowledge-graph-mvp"
version = "0.1.0"
description = "知识图谱构建系统MVP"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "streamlit>=1.28.1",
    "neo4j>=5.14.1",
    "requests>=2.31.0",
    "pyvis>=0.3.2",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.0",
]
requires-python = ">=3.8"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

# 配置镜像源（阿里云主源，清华备用源）
[[tool.uv.index]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true

[[tool.uv.index]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"
```

### uv.lock
```toml
# 此文件由uv自动生成，请勿手动编辑
# 运行 uv sync 时会自动创建
```

### docker-compose.yml（可选，仅Docker用户需要）
```yaml
# 如果你选择使用Docker部署Neo4j，可以使用此配置
# 推荐开发环境直接安装本地Neo4j

version: '3.8'

services:
  neo4j:
    image: neo4j:5.13-community
    container_name: neo4j-kg-mvp
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["apoc"]
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
```

### .env
```env
# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password123

# LLM配置 - 默认使用ModelScope
DEFAULT_LLM_PROVIDER=modelscope

# 智谱AI配置
ZHIPU_API_KEY=your_zhipu_api_key_here
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4/
ZHIPU_MODEL_NAME=GLM-4-Flash

# ModelScope配置
MODELSCOPE_API_KEY=your_modelscope_api_key_here
MODELSCOPE_BASE_URL=https://api-inference.modelscope.cn/v1/
MODELSCOPE_MODEL_NAME=Qwen/Qwen3-235B-A22B

# Ollama配置（可选）
OLLAMA_API_KEY=ollama
OLLAMA_BASE_URL=http://localhost:11434/v1
OLLAMA_MODEL_NAME=qwen3:1.7b

# OpenRouter配置
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL_NAME=qwen/qwen3-30b-a3b:free

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# Streamlit配置
STREAMLIT_PORT=8501
```

## 🔨 完整代码示例

### FastAPI主应用（src/main.py）
```python
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict
import os
from dotenv import load_dotenv

from services.llm_service import LLMService
from services.graph_service import GraphService

load_dotenv()

app = FastAPI(title="知识图谱MVP API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化服务
llm_service = LLMService()
graph_service = GraphService()

class DocumentRequest(BaseModel):
    text: str

class QueryRequest(BaseModel):
    question: str

@app.post("/api/documents/extract")
async def extract_knowledge(request: DocumentRequest):
    """从文档中提取知识图谱"""
    try:
        # 1. 使用LLM提取实体和关系
        extracted_data = llm_service.extract_entities_relations(request.text)

        # 2. 存储到图数据库
        for entity in extracted_data.get("entities", []):
            graph_service.add_entity(entity)

        for relation in extracted_data.get("relations", []):
            graph_service.add_relation(relation)

        return {
            "status": "success",
            "entities": extracted_data.get("entities", []),
            "relations": extracted_data.get("relations", [])
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/graph/visualize")
async def get_graph_visualization():
    """获取图谱可视化数据"""
    try:
        graph_data = graph_service.get_all_nodes_and_edges()
        return graph_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/graph/query")
async def query_graph(request: QueryRequest):
    """智能查询图谱"""
    try:
        # 简单的查询实现
        # 在实际应用中，这里需要更复杂的自然语言处理
        answer = llm_service.answer_question(request.question)
        return {"answer": answer}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 实体模型（src/models/entities.py）
```python
from pydantic import BaseModel
from typing import Optional, List

class Entity(BaseModel):
    name: str
    type: str
    description: Optional[str] = ""

class Relation(BaseModel):
    source: str
    target: str
    relation: str
    confidence: Optional[float] = 1.0

class KnowledgeGraph(BaseModel):
    entities: List[Entity]
    relations: List[Relation]
```

## 🚀 一键启动脚本

### start.sh (Linux/Mac)
```bash
#!/bin/bash

echo "🚀 启动知识图谱MVP系统..."

# 检查uv是否安装
if ! command -v uv &> /dev/null; then
    echo "📦 安装uv包管理器..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source $HOME/.cargo/env
fi

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装Docker"
    exit 1
fi

# 配置阿里镜像源
export UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
export UV_EXTRA_INDEX_URL=https://pypi.org/simple/

# 同步依赖
echo "📦 同步Python依赖..."
uv sync

# 启动Neo4j
echo "🗄️ 启动Neo4j数据库..."
docker-compose up -d neo4j

# 等待Neo4j启动
echo "⏳ 等待Neo4j启动..."
sleep 10

# 检查Ollama
if ! command -v ollama &> /dev/null; then
    echo "⚠️ Ollama 未安装，请手动安装并启动"
    echo "下载地址：https://ollama.ai/download"
    echo "安装后运行：ollama pull llama3.1:8b"
else
    echo "🤖 检查Ollama模型..."
    ollama pull llama3.1:8b
fi

# 启动后端API
echo "🔧 启动后端API..."
cd src
uv run uvicorn main:app --reload --port 8000 &
API_PID=$!

# 等待API启动
sleep 5

# 启动前端
echo "🌐 启动前端界面..."
cd ../frontend
uv run streamlit run app.py --server.port 8501 &
FRONTEND_PID=$!

echo "✅ 系统启动完成！"
echo "📊 前端界面: http://localhost:8501"
echo "🔧 API文档: http://localhost:8000/docs"
echo "🗄️ Neo4j浏览器: http://localhost:7474"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '🛑 正在停止服务...'; kill $API_PID $FRONTEND_PID; docker-compose down; exit" INT
wait
```

### start.bat (Windows)
```batch
@echo off
echo 🚀 启动知识图谱MVP系统...

REM 检查uv是否安装
uv --version >nul 2>&1
if errorlevel 1 (
    echo 📦 安装uv包管理器...
    powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
)

REM 配置阿里镜像源
set UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
set UV_EXTRA_INDEX_URL=https://pypi.org/simple/

REM 同步依赖
echo 📦 同步Python依赖...
uv sync

REM 启动Neo4j
echo 🗄️ 启动Neo4j数据库...
docker-compose up -d neo4j

REM 等待Neo4j启动
echo ⏳ 等待Neo4j启动...
timeout /t 10 /nobreak

REM 启动后端API
echo 🔧 启动后端API...
start "API Server" cmd /k "cd src && uv run uvicorn main:app --reload --port 8000"

REM 等待API启动
timeout /t 5 /nobreak

REM 启动前端
echo 🌐 启动前端界面...
start "Frontend" cmd /k "cd frontend && uv run streamlit run app.py --server.port 8501"

echo ✅ 系统启动完成！
echo 📊 前端界面: http://localhost:8501
echo 🔧 API文档: http://localhost:8000/docs
echo 🗄️ Neo4j浏览器: http://localhost:7474
pause
```

## 📚 新手教程

### 第一次运行指南

1. **准备工作**
   ```bash
   # 下载项目代码
   git clone <your-repo-url>
   cd knowledge-graph-mvp

   # 给启动脚本执行权限（Linux/Mac）
   chmod +x start.sh
   ```

2. **安装Ollama**
   - 访问 https://ollama.ai/download
   - 下载对应系统的安装包
   - 安装后运行：`ollama pull llama3.1:8b`

3. **启动系统**
   ```bash
   # Linux/Mac
   ./start.sh

   # Windows
   start.bat
   ```

4. **验证安装**
   - 打开 http://localhost:8501 查看前端
   - 打开 http://localhost:8000/docs 查看API文档
   - 打开 http://localhost:7474 查看Neo4j（用户名：neo4j，密码：password123）

### 使用教程

1. **上传文档**
   - 在前端选择"文档上传"
   - 上传一个txt文件（建议先用简单的测试文本）
   - 点击"提取知识图谱"

2. **查看图谱**
   - 切换到"图谱可视化"页面
   - 点击"加载图谱"查看提取的实体和关系

3. **智能查询**
   - 切换到"智能查询"页面
   - 输入自然语言问题
   - 查看AI的回答

## 🐛 故障排除

### 常见错误及解决方案

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8000  # 检查API端口
   lsof -i :8501  # 检查前端端口

   # 杀死占用进程
   kill -9 <PID>
   ```

2. **Neo4j连接失败**
   ```bash
   # 重置Neo4j密码
   docker exec -it neo4j-kg-mvp cypher-shell -u neo4j -p neo4j
   # 然后运行：ALTER USER neo4j SET PASSWORD 'password123'
   ```

3. **Ollama模型下载失败**
   ```bash
   # 手动下载模型
   ollama pull llama3.1:8b

   # 检查模型列表
   ollama list
   ```

4. **Python依赖安装失败**
   ```bash
   # 清理uv缓存
   uv cache clean

   # 重新同步依赖
   uv sync --reinstall

   # 如果仍有问题，尝试不使用缓存
   uv sync --no-cache
   ```

## 📈 性能优化建议

1. **LLM优化**
   - 使用更小的模型（如llama3.1:3b）提高速度
   - 优化提示词减少token消耗
   - 实现结果缓存避免重复计算

2. **数据库优化**
   - 为常用查询创建索引
   - 定期清理测试数据
   - 使用连接池管理数据库连接

3. **前端优化**
   - 实现分页显示大型图谱
   - 添加加载状态提示
   - 缓存可视化结果

这个MVP设计专注于核心功能，代码简单易懂，适合小白开发者快速上手。通过这个MVP，您可以快速验证技术方案的可行性，为后续的完整系统开发奠定基础。
