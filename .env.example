# 知识图谱构建系统环境配置
# 基于开源项目的配置示例

# =============================================================================
# 应用基础配置
# =============================================================================
APP_NAME="知识图谱构建系统"
APP_VERSION="0.1.0"
APP_ENV="development"  # development, production
LOG_LEVEL="INFO"
DEBUG=true

# =============================================================================
# Neo4j 图数据库配置（开源图数据库）
# =============================================================================
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="password"
NEO4J_DATABASE="neo4j"

# Neo4j 企业版配置（可选）
# NEO4J_ENTERPRISE=false
# NEO4J_CLUSTER_DISCOVERY_ENDPOINTS="localhost:7687"

# =============================================================================
# LLM 配置 - 生产环境（基于开源模型部署）
# =============================================================================

# vLLM 部署配置（推荐用于生产环境）
LLM_PROVIDER="vllm"
VLLM_MODEL="qwen2.5-7b-instruct"
VLLM_HOST="localhost"
VLLM_PORT=8000
VLLM_API_KEY="EMPTY"
VLLM_BASE_URL="http://localhost:8000/v1"

# Ollama 部署配置（适合开发和小规模部署）
# LLM_PROVIDER="ollama"
# OLLAMA_MODEL="qwen2.5:7b"
# OLLAMA_HOST="localhost"
# OLLAMA_PORT=11434
# OLLAMA_BASE_URL="http://localhost:11434/v1"

# =============================================================================
# LLM 配置 - 开发调试环境（第三方API服务）
# =============================================================================

# OpenAI API（开发调试用）
# LLM_PROVIDER="openai"
# OPENAI_API_KEY="your_openai_api_key_here"
# OPENAI_MODEL="gpt-3.5-turbo"
# OPENAI_BASE_URL="https://api.openai.com/v1"

# Azure OpenAI（企业用户）
# LLM_PROVIDER="azure_openai"
# AZURE_OPENAI_API_KEY="your_azure_api_key"
# AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com/"
# AZURE_OPENAI_API_VERSION="2023-12-01-preview"
# AZURE_OPENAI_DEPLOYMENT_NAME="gpt-35-turbo"

# 智谱AI GLM（国产API）
# LLM_PROVIDER="zhipu"
# ZHIPU_API_KEY="your_zhipu_api_key"
# ZHIPU_MODEL="glm-4"
# ZHIPU_BASE_URL="https://open.bigmodel.cn/api/paas/v4/"

# DeepSeek API（国产API）
# LLM_PROVIDER="deepseek"
# DEEPSEEK_API_KEY="your_deepseek_api_key"
# DEEPSEEK_MODEL="deepseek-chat"
# DEEPSEEK_BASE_URL="https://api.deepseek.com/v1"

# 百度文心一言（国产API）
# LLM_PROVIDER="baidu"
# BAIDU_API_KEY="your_baidu_api_key"
# BAIDU_SECRET_KEY="your_baidu_secret_key"
# BAIDU_MODEL="ernie-3.5-8k"

# =============================================================================
# 向量数据库配置（基于开源向量搜索引擎）
# =============================================================================

# ChromaDB 配置（AI原生向量数据库）
VECTOR_DB_PROVIDER="chroma"
CHROMA_HOST="localhost"
CHROMA_PORT=8000
CHROMA_COLLECTION_NAME="knowledge_graph_docs"

# Faiss 配置（Meta开源向量搜索）
# VECTOR_DB_PROVIDER="faiss"
# FAISS_INDEX_PATH="./data/faiss_index"
# FAISS_DIMENSION=1536

# Qdrant 配置（备选向量数据库）
# VECTOR_DB_PROVIDER="qdrant"
# QDRANT_HOST="localhost"
# QDRANT_PORT=6333
# QDRANT_COLLECTION_NAME="knowledge_graph"

# =============================================================================
# 文档处理配置（基于开源文档解析工具）
# =============================================================================

# MinerU 配置（高保真文档解析）
DOCUMENT_PARSER="mineru"
MINERU_CONFIG_PATH="./config/mineru_config.json"
MINERU_OUTPUT_DIR="./data/parsed_documents"

# PyMuPDF 配置（标准PDF处理）
# DOCUMENT_PARSER="pymupdf"
# PYMUPDF_EXTRACT_IMAGES=true
# PYMUPDF_EXTRACT_TABLES=true

# Unstructured 配置（备选文档处理）
# DOCUMENT_PARSER="unstructured"
# UNSTRUCTURED_API_KEY="your_unstructured_api_key"

# =============================================================================
# 社区检测算法配置（基于开源图算法库）
# =============================================================================

# Leiden 算法配置（推荐）
COMMUNITY_DETECTION_ALGORITHM="leiden"
LEIDEN_RESOLUTION=1.0
LEIDEN_RANDOM_STATE=42

# Louvain 算法配置（经典算法）
# COMMUNITY_DETECTION_ALGORITHM="louvain"
# LOUVAIN_RESOLUTION=1.0
# LOUVAIN_RANDOM_STATE=42

# NetworkX 算法配置（备选）
# COMMUNITY_DETECTION_ALGORITHM="networkx_greedy"

# =============================================================================
# Web服务配置（基于现代Python框架）
# =============================================================================

# FastAPI 配置
API_HOST="0.0.0.0"
API_PORT=8080
API_WORKERS=4
API_RELOAD=true

# Streamlit 配置
STREAMLIT_HOST="0.0.0.0"
STREAMLIT_PORT=8501
STREAMLIT_THEME="light"

# =============================================================================
# 缓存和队列配置（基于开源中间件）
# =============================================================================

# Redis 配置（缓存和消息队列）
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=""

# Celery 配置（分布式任务队列）
CELERY_BROKER_URL="redis://localhost:6379/0"
CELERY_RESULT_BACKEND="redis://localhost:6379/0"

# =============================================================================
# 监控和日志配置
# =============================================================================

# 结构化日志配置
LOG_FORMAT="json"  # json, text
LOG_FILE="./logs/app.log"
LOG_MAX_SIZE="100MB"
LOG_BACKUP_COUNT=5

# 性能监控
ENABLE_METRICS=true
METRICS_PORT=9090

# 健康检查
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# =============================================================================
# 开发工具配置
# =============================================================================

# Poetry 配置
POETRY_VENV_IN_PROJECT=true
POETRY_CACHE_DIR="./cache/poetry"

# pytest 配置
PYTEST_ADDOPTS="--cov=src --cov-report=html --cov-report=term"

# Black 代码格式化
BLACK_LINE_LENGTH=88
BLACK_TARGET_VERSION="py310"

# MyPy 类型检查
MYPY_STRICT=true
MYPY_IGNORE_MISSING_IMPORTS=true

# =============================================================================
# Docker 部署配置
# =============================================================================

# Docker Compose 配置
COMPOSE_PROJECT_NAME="knowledge-graph-builder"
COMPOSE_FILE="docker-compose.yml"

# 容器资源限制
CONTAINER_MEMORY_LIMIT="4g"
CONTAINER_CPU_LIMIT="2"

# =============================================================================
# 安全配置
# =============================================================================

# API 安全
API_SECRET_KEY="your-secret-key-change-in-production"
API_ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS 配置
CORS_ORIGINS="http://localhost:3000,http://localhost:8501"
CORS_ALLOW_CREDENTIALS=true

# 数据加密
ENCRYPTION_KEY="your-encryption-key-32-chars-long"

# =============================================================================
# 生产环境配置
# =============================================================================

# 负载均衡
LOAD_BALANCER_ENABLED=false
LOAD_BALANCER_ALGORITHM="round_robin"

# 自动扩缩容
AUTO_SCALING_ENABLED=false
AUTO_SCALING_MIN_REPLICAS=1
AUTO_SCALING_MAX_REPLICAS=10
AUTO_SCALING_TARGET_CPU=70

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点
BACKUP_RETENTION_DAYS=30
