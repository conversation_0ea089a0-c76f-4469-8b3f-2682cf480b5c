OPENAI_API_KEY = 'sk-xxx'
OPENAI_BASE_URL = 'http://localhost:13000/v1'

OPENAI_EMBEDDINGS_MODEL = 'text-embedding-3-large'
OPENAI_LLM_MODEL = 'gpt-4o'

TEMPERATURE = 0
MAX_TOKENS = 2000

VERBOSE = True

NEO4J_URI='neo4j://localhost:7687'
NEO4J_USERNAME='neo4j'
NEO4J_PASSWORD='12345678'

# 缓存向量相似度匹配配置
# 可选值: 'openai' (复用RAG的向量模型), 'sentence_transformer' (使用本地模型)
CACHE_EMBEDDING_PROVIDER = 'openai'
# 当使用sentence_transformer时的模型名，模型会缓存到 ./cache/model 目录
CACHE_SENTENCE_TRANSFORMER_MODEL = 'all-MiniLM-L6-v2'

# 模型缓存配置
MODEL_CACHE_ROOT = './cache'  # 缓存根目录，模型会保存到 {MODEL_CACHE_ROOT}/model

LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="xxx"
LANGSMITH_PROJECT="xxx"