# 演示数据准备指南

## 演示数据的重要性

演示数据是客户演示成功的关键因素之一。好的演示数据能够：
- 让客户直观感受产品功能
- 增强客户对产品的信任度
- 帮助客户理解应用场景
- 促进客户做出购买决策

## 演示数据准备原则

### 1. 行业相关性
- 选择与客户行业相关的数据
- 使用客户熟悉的业务术语
- 模拟客户实际业务场景

### 2. 数据真实性
- 使用真实但脱敏的数据
- 避免使用明显的测试数据
- 确保数据逻辑合理

### 3. 功能完整性
- 覆盖产品的核心功能
- 展示不同类型的查询场景
- 包含复杂和简单的示例

### 4. 演示效果
- 数据量适中，不过多不过少
- 查询结果清晰易懂
- 可视化效果美观

## 不同行业的演示数据模板

### 金融行业

#### 实体类型
- **人员**：客户、员工、高管
- **机构**：银行、公司、监管机构
- **产品**：贷款、理财、保险
- **地区**：省份、城市、区域

#### 关系类型
- 工作关系：任职、投资、控股
- 业务关系：开户、贷款、担保
- 地理关系：注册地、经营地
- 风险关系：关联交易、异常行为

#### 样例数据
```
实体：
- 张三（个人客户）
- ABC投资公司（企业客户）
- 李四（公司法人）
- 北京分行（银行机构）

关系：
- 张三 --开户--> 北京分行
- 李四 --担任法人--> ABC投资公司
- ABC投资公司 --申请贷款--> 北京分行
- 张三 --为...担保--> ABC投资公司
```

#### 演示查询
1. "张三有哪些关联企业？"
2. "ABC投资公司的风险关联方有哪些？"
3. "北京分行的高风险客户有哪些？"

### 制造业

#### 实体类型
- **产品**：设备型号、零部件、原材料
- **人员**：工程师、技术专家、客户
- **流程**：生产工艺、质检流程、维修流程
- **问题**：故障类型、质量问题、安全隐患

#### 关系类型
- 组成关系：包含、组装、配套
- 因果关系：导致、影响、解决
- 责任关系：负责、维护、检修
- 时序关系：前置、后续、并行

#### 样例数据
```
实体：
- CNC-2000（数控机床）
- 主轴故障（故障类型）
- 王工程师（维修专家）
- 轴承磨损（故障原因）

关系：
- CNC-2000 --出现--> 主轴故障
- 主轴故障 --由...引起--> 轴承磨损
- 王工程师 --擅长维修--> CNC-2000
- 轴承磨损 --解决方案--> 更换轴承
```

#### 演示查询
1. "CNC-2000常见故障有哪些？"
2. "主轴故障的解决方案是什么？"
3. "王工程师负责哪些设备的维修？"

### 医疗健康

#### 实体类型
- **疾病**：疾病名称、症状、并发症
- **药物**：药品名称、成分、剂型
- **检查**：检验项目、影像检查、生理指标
- **治疗**：治疗方案、手术方式、康复措施

#### 关系类型
- 诊断关系：症状、体征、检查结果
- 治疗关系：适应症、禁忌症、副作用
- 药物关系：相互作用、配伍禁忌
- 预后关系：并发症、预防措施

#### 样例数据
```
实体：
- 高血压（疾病）
- 头痛（症状）
- 降压药（药物类别）
- 血压监测（检查项目）

关系：
- 高血压 --常见症状--> 头痛
- 高血压 --治疗药物--> 降压药
- 高血压 --需要检查--> 血压监测
- 降压药 --副作用--> 低血压
```

#### 演示查询
1. "高血压的常见症状有哪些？"
2. "降压药有什么副作用？"
3. "头痛可能是什么疾病的症状？"

## 演示环境搭建

### 数据准备步骤

#### 1. 文档准备
- 准备10-20个相关领域的文档
- 文档类型：PDF、Word、PPT、Excel
- 文档内容：产品手册、技术规范、案例分析等
- 文档大小：每个文档5-50页

#### 2. 知识图谱构建
```bash
# 启动系统服务
docker-compose up -d

# 上传文档
curl -X POST "http://localhost:8080/documents/upload" \
  -F "files=@demo_doc1.pdf" \
  -F "files=@demo_doc2.docx"

# 构建知识图谱
curl -X POST "http://localhost:8080/knowledge-graph/build" \
  -H "Content-Type: application/json" \
  -d '{"document_ids": ["doc1", "doc2"]}'

# 等待构建完成
curl -X GET "http://localhost:8080/knowledge-graph/status"
```

#### 3. 验证数据质量
- 检查实体数量（建议500-2000个）
- 检查关系数量（建议1000-5000个）
- 验证查询响应速度
- 测试可视化效果

### 演示查询准备

#### 简单查询（展示基础功能）
1. "张三是谁？"
2. "ABC公司的基本信息"
3. "有哪些产品型号？"

#### 复杂查询（展示高级功能）
1. "张三和李四有什么关系？"
2. "ABC公司的风险关联方有哪些？"
3. "CNC-2000的故障原因和解决方案"

#### 推理查询（展示智能能力）
1. "如果张三离职，会影响哪些项目？"
2. "ABC公司如果违约，会影响哪些关联方？"
3. "主轴故障可能导致什么后果？"

## 演示脚本准备

### 功能演示顺序

#### 1. 文档上传演示（2分钟）
- 展示多格式文档上传
- 显示文档解析进度
- 展示解析结果预览

#### 2. 知识图谱构建（3分钟）
- 启动知识图谱构建
- 展示构建进度和统计
- 显示构建完成的图谱概览

#### 3. 图谱可视化（3分钟）
- 展示整体图谱结构
- 演示节点和关系的交互
- 展示不同类型实体的颜色区分

#### 4. 智能问答（5分钟）
- 从简单查询开始
- 逐步展示复杂查询
- 演示推理和分析能力

#### 5. 高级功能（2分钟）
- 社区检测结果
- 统计分析报表
- 数据导出功能

### 演示话术准备

#### 开场白
"现在我来为大家演示我们的知识图谱构建系统。我准备了一些与贵公司业务相关的样例数据，让大家能够直观地感受系统的功能和效果。"

#### 功能介绍
"首先，我们来看文档处理功能。系统支持多种格式的文档，包括PDF、Word、Excel等。我这里准备了几个[行业]相关的文档..."

#### 查询演示
"接下来我们来测试智能问答功能。我先问一个简单的问题：'张三是谁？'...大家可以看到，系统不仅给出了答案，还显示了相关的知识图谱..."

#### 效果总结
"通过刚才的演示，大家可以看到，我们的系统能够自动从文档中提取知识，构建图谱，并支持智能问答。整个过程完全自动化，大大提高了知识管理的效率。"

## 常见问题准备

### 技术问题
1. **Q: 数据准确率如何？**
   A: 演示数据显示，实体识别准确率95%+，关系抽取准确率90%+。实际项目中会根据业务需求进行调优。

2. **Q: 处理速度如何？**
   A: 刚才演示的文档，平均每页处理时间约10秒。大规模部署时支持并行处理，速度会更快。

3. **Q: 支持哪些文档格式？**
   A: 支持PDF、Word、Excel、PPT、TXT等常见格式，以及图片中的文字识别。

### 业务问题
1. **Q: 如何保证数据质量？**
   A: 系统提供数据质量评估和人工校验功能。同时支持增量学习，持续改进准确率。

2. **Q: 能否与现有系统集成？**
   A: 完全可以。我们提供标准的API接口，支持与OA、ERP、CRM等系统集成。

3. **Q: 实施周期多长？**
   A: 根据数据规模和需求复杂度，一般6-10周。我们有标准化的实施流程。

## 演示注意事项

### 技术注意事项
1. **网络环境**：确保网络稳定，准备4G热点备用
2. **设备准备**：笔记本电脑、投影仪、激光笔、延长线
3. **软件准备**：浏览器、演示软件、录屏软件
4. **数据备份**：准备离线演示数据和视频录屏

### 演示技巧
1. **时间控制**：每个功能演示控制在2-3分钟
2. **互动参与**：邀请客户提出查询问题
3. **效果强调**：重点强调查询速度和结果准确性
4. **问题处理**：遇到技术问题要冷静处理，有备用方案

### 客户关注点
1. **数据安全**：强调内网部署，数据不出网
2. **实施风险**：展示成功案例，降低客户担忧
3. **投资回报**：结合演示效果说明价值
4. **技术支持**：承诺提供完善的技术支持

## 演示效果评估

### 客户反馈收集
- 功能满意度评分
- 技术可行性认可度
- 业务价值认同度
- 购买意向强度

### 改进建议
- 根据客户反馈调整演示内容
- 优化演示数据和查询示例
- 改进演示流程和话术
- 加强薄弱环节的准备

通过充分的演示数据准备和演示脚本练习，能够大大提高客户演示的成功率，促进项目成交。
