[tool.poetry]
name = "graph-maker"
version = "0.1.0"
description = "Grate graph out of any text using a given ontology"
authors = ["Nayak <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
langchain = "^0.0.335"
pandas = "^2.1.3"
numpy = "^1.26.2"
pypdf = "^3.17.0"
unstructured = "^0.10.30"
pathlib = "^1.0.1"
networkx = "^3.2.1"
pyvis = "^0.3.2"
seaborn = "^0.13.0"
uuid = "^1.30"
jupyterlab = "^4.0.8"
yachalk = "^0.1.5"
groq = "^0.5.0"
python-dotenv = "^1.0.1"
neo4j = "5.19.0"
neomodel = "^5.3.0"
logging = "^*******"
openai = "^1.28.0"
knowledge-graph-maker = "^0.1.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
