[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "knowledge-graph-builder"
version = "0.1.0"
description = "企业级知识图谱构建系统 - 基于开源生态，不重复造轮子"
authors = ["Knowledge Graph Team <<EMAIL>>"]
readme = "README.md"
license = "MIT"
homepage = "https://github.com/example/knowledge-graph-builder"
repository = "https://github.com/example/knowledge-graph-builder"
documentation = "https://knowledge-graph-builder.readthedocs.io"
keywords = ["knowledge-graph", "graph-rag", "llm", "neo4j", "langchain"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Database :: Database Engines/Servers",
]

[tool.poetry.dependencies]
python = "^3.10"

# AI/ML 框架 - 基于成熟开源项目
langchain = "^0.1.0"                    # AI应用开发标准框架
langgraph = "^0.1.0"                    # 智能体编排框架
openai = "^1.0.0"                       # OpenAI官方SDK
transformers = "^4.30.0"                # Hugging Face预训练模型库
sentence-transformers = "^2.2.0"        # 语义嵌入模型

# 图数据库和图算法 - 使用权威实现
neo4j = "^5.0.0"                        # Neo4j官方Python驱动
networkx = "^3.0"                       # Python图分析标准库
leidenalg = "^0.10.0"                   # Leiden算法官方实现
python-louvain = "^0.16"                # Louvain算法标准实现

# 文档处理 - 集成专业工具
PyMuPDF = "^1.23.0"                     # PDF处理业界标准
python-docx = "^0.8.11"                 # Word文档处理官方库
Pillow = "^10.0.0"                      # Python图像处理标准库
pytesseract = "^0.3.10"                 # OCR文字识别

# 数据处理和科学计算 - Python生态标准
pandas = "^2.0.0"                       # 数据分析标准库
numpy = "^1.24.0"                       # 科学计算基础库
scikit-learn = "^1.3.0"                 # 机器学习算法库

# 向量数据库 - 高性能向量搜索
faiss-cpu = "^1.7.4"                    # Meta开源向量搜索引擎
chromadb = "^0.4.0"                     # AI原生向量数据库

# Web框架和API - 现代Python框架
fastapi = "^0.100.0"                    # 现代Web API框架
streamlit = "^1.25.0"                   # 数据应用快速开发框架
uvicorn = "^0.23.0"                     # ASGI服务器

# 数据验证和配置 - 标准库
pydantic = "^2.0.0"                     # 数据验证标准库
python-dotenv = "^1.0.0"                # 环境变量管理
PyYAML = "^6.0"                         # YAML配置文件处理

# 异步和并发处理
aiohttp = "^3.8.0"                      # 异步HTTP客户端
asyncio-throttle = "^1.0.2"             # 异步限流控制

[tool.poetry.group.dev.dependencies]
# 开发和测试工具 - 基于Python生态标准
pytest = "^7.4.0"                       # Python测试标准框架
pytest-asyncio = "^0.21.0"              # 异步测试支持
pytest-cov = "^4.1.0"                   # 测试覆盖率
black = "^23.0.0"                       # 代码格式化官方工具
isort = "^5.12.0"                       # 导入排序工具
mypy = "^1.5.0"                         # 静态类型检查
flake8 = "^6.0.0"                       # 代码质量检查
pre-commit = "^3.3.0"                   # Git钩子管理

[tool.poetry.group.docs.dependencies]
# 文档生成 - 基于标准工具
mkdocs = "^1.5.0"                       # 文档生成工具
mkdocs-material = "^9.0.0"              # Material主题
mkdocstrings = "^0.22.0"                # API文档生成

[tool.poetry.extras]
# 可选依赖组
gpu = ["faiss-gpu", "torch"]            # GPU加速支持
enterprise = ["redis", "celery"]        # 企业级功能
mineru = ["magic-pdf"]                  # MinerU文档解析支持

[tool.poetry.scripts]
# 命令行工具
kg-build = "knowledge_graph_builder.cli:main"
kg-serve = "knowledge_graph_builder.api:serve"
